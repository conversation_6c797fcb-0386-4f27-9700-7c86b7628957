#基础镜像
FROM mathink/jre-fonts:jre-8
#SpringBoot项目必须使用/tmp目录
# 这里的 /tmp 目录就会在运行时自动挂载为匿名卷，任何向 /tmp 中写入的信息都不会记录进容器存储层
VOLUME /tmp
VOLUME /config
COPY target/fm-maintain-1.0.0-SNAPSHOT.jar /fm-maintain-1.0.10-SNAPSHOT.jar
RUN bash -c "touch /fm-maintain-1.0.10-SNAPSHOT.jar"
#暴露的端口
EXPOSE 23456
#解决时间不正确的问题
ENV TZ='Asia/Shanghai'

ENTRYPOINT ["java","-jar","/fm-maintain-1.0.10-SNAPSHOT.jar","--spring.config.location=/config/application.yml"]


##基础镜像，如果本地仓库没有，会从远程仓库拉取
#
#FROM openjdk:8-jdk-alpine
#
##容器中创建目录
#
#RUN mkdir -p /usr/local/jar
#
##编译后的jar包copy到容器中创建到目录内
#
#COPY target/fm-maintain-1.0.0-SNAPSHOT.jar /usr/local/jar/fm-maintain-1.0.0-SNAPSHOT.jar
#
#ENV TZ='Asia/Shanghai'
##指定容器启动时要执行的命令