apiVersion: apps/v1
kind: Deployment
metadata:
  name: fm2-cluser         #deployment名字
  namespace: fm2-cluser            #对应k8s Services命名空间
  labels:
    app: fm2-cluser        #deployment标签，可以自由定义
spec:
  replicas: 1               #pod 副本数量
  selector:                 #pod选择器定义，主要用于定义根据什么标签搜索需要管理的pod
    matchLabels:
      app: fm2-cluser      #pod标签
  template:                 #pod模版定义
    metadata:               #对象的元数据
      name: fm2-cluser     #pod 标签定义
      labels:
        app: fm2-cluser
    spec:
      containers:           #容器数组定义
        - name: fm2-cluser   #容器名
          image: 192.168.1.201:31388/fm2-cluser:v1.0.0-dev  #镜像地址
          imagePullPolicy: Always
          volumeMounts:
            - mountPath: "/config"  #容器配置文件路径
              name: config          #容器配置文件名称
          ports:
            - name: http
              containerPort: 23456  #定义容器需要暴露的端口
            - name: xxl-job
              containerPort: 9998  #定义容器需要暴露的端口
      volumes:                      #配置文件相关挂载
        - name: config              #和上面 name: config 一致 上下文关系
          configMap:
            name: fm2-cluser      #对应 k8s挂载的Config Maps名称
            items:
              - key: application.yml  #默认不变 内部配置文件
                path: application.yml

      restartPolicy: Always   #重启策略
        #Always pod中容器不论如何停止都将自动重启 （默认选这个就行了）
        #OnFailure Pod中容器非正常停止会自动重启，正常停止不会重启
      #Never  Pod中容器不论以任何方式停止，都不会自动重启
---
apiVersion: v1
kind: Service     #配置对应网络相关
metadata:
  name: fm2-cluser-service #服务名 后期在k8s里查看的名称
  namespace: fm2-cluser  #对应k8s Services命名空间
spec:
  selector:           #pod选择器定义，由这里决定请求转发给那些pod处理
    app: fm2-cluser  #pod 标签 指定上文标签配置
  ports:              #服务端口定义
    - protocol: TCP   #协议类型，主要就是TCP和UDP
      port: 23456     # 容器间，服务调用的端口 直白说就是容器的内部端口
      nodePort: 23458 #pod 容器暴露的端口 直白的说就是外部访问接口
  type: NodePort      #配置为NodePort，外部可以访问
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: fm21-test         #deployment名字
  namespace: fm21-test            #对应k8s Services命名空间
  labels:
    app: fm21-test        #deployment标签，可以自由定义
spec:
  replicas: 1               #pod 副本数量
  selector:                 #pod选择器定义，主要用于定义根据什么标签搜索需要管理的pod
    matchLabels:
      app: fm21-test      #pod标签
  template:                 #pod模版定义
    metadata:               #对象的元数据
      name: fm21-test     #pod 标签定义
      labels:
        app: fm21-test
    spec:
      imagePullSecrets:
        - name: regcred
      containers:           #容器数组定义
        - name: fm21-test   #容器名
          image: 192.168.1.201:31388/fm21-test:v1.0.0-dev  #镜像地址
          imagePullPolicy: Always
          volumeMounts:
            - mountPath: "/config"  #容器配置文件路径
              name: config          #容器配置文件名称
          ports:
            - name: http
              containerPort: 23456  #定义容器需要暴露的端口
            - name: xxl-job
              containerPort: 9998  #定义容器需要暴露的端口
      volumes:                      #配置文件相关挂载
        - name: config              #和上面 name: config 一致 上下文关系
          configMap:
            name: fm21-test      #对应 k8s挂载的Config Maps名称
            items:
              - key: application.yml  #默认不变 内部配置文件
                path: application.yml

      restartPolicy: Always   #重启策略
      #Always pod中容器不论如何停止都将自动重启 （默认选这个就行了）
      #OnFailure Pod中容器非正常停止会自动重启，正常停止不会重启
      #Never  Pod中容器不论以任何方式停止，都不会自动重启
---
apiVersion: v1
kind: Service     #配置对应网络相关
metadata:
  name: fm21-test-service #服务名 后期在k8s里查看的名称
  namespace: fm21-test  #对应k8s Services命名空间
spec:
  selector:           #pod选择器定义，由这里决定请求转发给那些pod处理
    app: fm21-test  #pod 标签 指定上文标签配置
  ports:              #服务端口定义
    - protocol: TCP   #协议类型，主要就是TCP和UDP
      port: 23456     # 容器间，服务调用的端口 直白说就是容器的内部端口
      nodePort: 23459 #pod 容器暴露的端口 直白的说就是外部访问接口
  type: NodePort      #配置为NodePort，外部可以访问
