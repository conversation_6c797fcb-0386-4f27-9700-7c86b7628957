stages:
  - clean
  - path
  - dockerbuild
  - deploy
  # - reclean
workflow:
  rules:
    - if:  $MANUAL == 'true' || $CI_PIPELINE_SOURCE == 'merge_request_event' || $CI_COMMIT_BRANCH ==  'fm-cluster-coe' || $CI_COMMIT_BRANCH ==  'master' || $CI_COMMIT_BRANCH ==  'fm-cluster-coe2'

fm-maintain-maven-clean:
  stage: clean
  tags:
  - gitlabrunner02-ssd
  script:
    - echo "清理中ing.........."
    - mvn clean -B -Dmaven.test.skip=true --settings /etc/maven/settings.xml
maven-getpath-dev:
    stage: path
    script:
      - echo "VERSION=${FMCLUSTERCOEDEVVERSION}" >> build.env
    artifacts:
      reports:
        dotenv: build.env
    rules:
      - if :  $CI_COMMIT_BRANCH !=  'master'
    tags:
    - gitlabrunner02-ssd
# maven-getpath-pre: 没有预发分支
#    stage: path
#    script:
#      - echo "VERSION=${PRERELEASEVERSION}" >> build.env
#    artifacts:
#      reports:
#        dotenv: build.env
#    rules:
#      - if :  $CI_COMMIT_BRANCH ==  'maintain-f-d-21946'
maven-getpath-prd:
    stage: path
    script:
      - echo "VERSION=${FMCLUSTERCOEDEVVERSION}" >> build.env
    artifacts:
      reports:
        dotenv: build.env
    rules:
      - if :  $CI_COMMIT_BRANCH ==  'master'
    tags:
    - gitlabrunner02-ssd
fm-maintain-docker-build:
  stage: dockerbuild
  script:
    - echo "构建镜像ing" + ${VERSION}
    - mvn package -B -Dmaven.test.skip=true --settings /etc/maven/settings.xml
    - 'docker build -t "${registry}/fm2-cluser:${VERSION}" .'
    - 'docker push "${registry}/fm2-cluser:${VERSION}"'
  rules:
    - if :  $MANUAL == 'true' || $CI_PIPELINE_SOURCE == 'merge_request_event' || $CI_COMMIT_BRANCH ==  'fm-cluster-coe'
  tags:
  - gitlabrunner02-ssd
fm-maintain-docker-build2:
  stage: dockerbuild
  script:
    - echo "构建镜像2ing v1.0.0-dev"
    - mvn package -B -Dmaven.test.skip=true --settings /etc/maven/settings.xml
    - 'docker build -t "${registry}/fm21-test:v1.0.0-dev" .'
    - 'docker push "${registry}/fm21-test:v1.0.0-dev"'
  rules:
    - if :  $MANUAL == 'true' || $CI_PIPELINE_SOURCE == 'merge_request_event' || $CI_COMMIT_BRANCH ==  'fm-cluster-coe2'
  tags:
    - gitlabrunner02-ssd
  # needs:
  #   - fm-maintain-docker-build
fm-maintain-docker-deploy:
  stage: deploy
  script:
    - 'kubectl --kubeconfig /home/<USER>/k8s/config config use-context kubernetes-admin@kubernetes'
    - 'kubectl --kubeconfig /home/<USER>/k8s/config -n fm2-cluser  set image deployment/fm2-cluser fm2-cluser=${registry}/fm2-cluser:${VERSION}'
    - 'kubectl --kubeconfig /home/<USER>/k8s/config -n fm2-cluser delete pod -l app=fm2-cluser'
  tags:
  - gitlabrunner02-ssd
  rules:
    - if :  $MANUAL == 'true' || $CI_PIPELINE_SOURCE == 'merge_request_event' || $CI_COMMIT_BRANCH ==  'fm-cluster-coe'
fm-maintain-docker-deploy2:
  stage: deploy
  script:
    - 'kubectl --kubeconfig /home/<USER>/k8s/config config use-context kubernetes-admin@kubernetes'
    - 'kubectl --kubeconfig /home/<USER>/k8s/config -n fm21-test  set image deployment/fm21-test fm21-test=${registry}/fm21-test:v1.0.0-dev'
    - 'kubectl --kubeconfig /home/<USER>/k8s/config -n fm21-test delete pod -l app=fm21-test'
  tags:
    - gitlabrunner02-ssd
  rules:
    - if :  $MANUAL == 'true' || $CI_PIPELINE_SOURCE == 'merge_request_event' || $CI_COMMIT_BRANCH ==  'fm-cluster-coe2'
# fm-maintain-reclean:
#   stage: reclean
#   tags:
#   - gitlabrunner02-ssd
#  script:
#    - 'curl -H ''Content-type: application/json'' -d ''{"msgtype":"text", "text": {"content":"DevOps:应用已成功部署到Dev环境,访问地址:http://*************:23458"}}'' https://oapi.dingtalk.com/robot/send?access_token=dac9801d642ab7190fa0ad26957746b48663c304dd56a6065e7508ba7c9c658e'
# project-reclean:
#   stage: reclean
#   script:
#     - "docker images|grep none|awk '{print $3 }'|xargs docker rmi"
