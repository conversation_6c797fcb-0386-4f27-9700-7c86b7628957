<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.facilityone.fm.maintain.servicecenter.dao.WeChatIntegralMapper">


    <select id="findUserPoint" resultType="cn.facilityone.fm.maintain.servicecenter.dto.WeChatIntegralDTO">
        SELECT
                wup.open_id AS openId ,
                wup.created_date AS createTime ,
                wup.point AS point ,
                info.nick_name AS nickName
        FROM
        wechat_user_project wup
        LEFT JOIN wechat_user_info info ON wup.open_id = info.open_id
        WHERE
        wup.proj_id = #{CurrentProject}
        <if test="condition.openId != null and condition.openId != ''">
            AND wup.open_id like concat('%',#{condition.openId},'%')
        </if>
        <if test="condition.createTime != null and condition.createTime != ''">
            AND DATE (wup.created_date)=#{condition.createTime}
        </if>
        <if test="condition.point != null and condition.point != ''">
            AND wup.point = #{condition.point}
        </if>
        <if test="condition.nickName != null and condition.nickName != ''">
            AND info.nick_name like concat('%',#{condition.nickName},'%')
        </if>

    </select>
    <select id="findUserPointHistory"
            resultType="cn.facilityone.fm.maintain.servicecenter.dto.WeChatIntegralHistoryDTO">
        SELECT
                created_date AS time ,
                now_point AS nowPoint ,
                (
                    CASE
                    WHEN reword_type = 0 THEN
                        point
                    ELSE
                        - point
                    END
                ) AS point ,
                (
                    CASE
                    WHEN created_name IS NULL THEN
                        '系统'
                    ELSE
                        created_name
                    END
                ) AS createdName ,
                description
        FROM
            wechat_point
        WHERE
            open_id = #{openId}

        AND proj_id = #{CurrentProject}
        <if test="condition.createdName != null and condition.createdName != ''">
            AND created_name like concat('%',#{condition.createdName},'%')
        </if>
        <if test="condition.time != null and condition.time != ''">
            AND DATE (created_date)=#{condition.time}
        </if>
        <if test="condition.point != null and condition.point != ''">
            AND point = #{condition.point}
        </if>
        <if test="condition.description != null and condition.description != ''">
            AND description like concat('%',#{condition.description},'%')
        </if>
        <if test="condition.nowPoint != null and condition.nowPoint != ''">
            AND now_point =#{condition.nowPoint}
        </if>
        ORDER  BY created_date DESC
    </select>
</mapper>