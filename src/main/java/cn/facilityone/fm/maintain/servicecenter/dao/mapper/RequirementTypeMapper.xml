<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.facilityone.fm.maintain.servicecenter.dao.RequirementTypeMapper">

    <resultMap id="baseMap" type="cn.facilityone.fm.maintain.servicecenter.entity.RequirementType">
        <id column="reqtype_id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="can_deleted" property="canDeleted" jdbcType="BOOLEAN"/>
        <result column="full_name" property="fullName" jdbcType="VARCHAR"/>
        <result column="gen_wo" property="genWo" jdbcType="BOOLEAN"/>
        <result column="sort" property="sort" jdbcType="INTEGER"/>
        <result column="wc_type" property="wcType" jdbcType="INTEGER"/>
        <result column="parent_reqtype_id" property="parentReqtypeId" jdbcType="BIGINT"/>
        <result column="stype_id" property="stypeId" jdbcType="BIGINT"/>
        <result column="proj_id" property="project" jdbcType="BIGINT"/>
        <result column="deleted" property="deleted" jdbcType="TINYINT"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="created_date" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="modifier" property="modifier" jdbcType="VARCHAR"/>
        <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP"/>
        <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
    </resultMap>

    <insert id="insertHandleWorkTeams">
        INSERT requirement_type_wo_team (reqtype_id,work_team_id) VALUES
        <foreach collection="workTeamIds" item="teamId" separator=",">
            (#{id},#{teamId})
        </foreach>
    </insert>

    <delete id="clearHandleWorkTeams">
        DELETE
        FROM requirement_type_wo_team
        WHERE reqtype_id = #{id}
    </delete>

    <delete id="clearHandleWorkTeamsByTypeIds">
        DELETE
        FROM requirement_type_wo_team
        WHERE reqtype_id in
        <foreach collection="typeIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>


    <select id="selectHandleWorkTeams" resultType="cn.facilityone.fm.maintain.organize.entity.WorkTeam">
        SELECT DISTINCT work_team.work_team_id   AS 'id',
                        work_team.work_team_name AS 'name',
                        work_team.work_team_desc AS 'description'
        FROM requirement_type_wo_team
                 LEFT JOIN work_team ON work_team.work_team_id = requirement_type_wo_team.work_team_id
        WHERE work_team.deleted = 0
          AND requirement_type_wo_team.reqtype_id = #{id}
    </select>

    <select id="selectHandleWorkTeamIds" resultType="java.lang.Long">
        SELECT DISTINCT work_team.work_team_id
        FROM requirement_type_wo_team
                 LEFT JOIN work_team ON work_team.work_team_id = requirement_type_wo_team.work_team_id
        WHERE work_team.deleted = 0
          AND work_team.proj_id = #{CurrentProject}
          AND requirement_type_wo_team.reqtype_id = #{id}
    </select>

    <select id="findMaxSortNumber" resultType="java.lang.Integer">
        SELECT MAX(sort) FROM requirement_type
        WHERE
        deleted = FALSE
        AND proj_id = #{CurrentProject}
        <if test="parentId == null">
            AND parent_reqtype_id IS NULL
        </if>
        <if test="parentId != null">
            AND parent_reqtype_id = #{parentId}
        </if>
    </select>

    <select id="findMaxSortNumberHome" resultType="java.lang.Integer">
        SELECT MAX(sort) FROM requirement_type
        WHERE
        deleted = 0
        AND proj_id = 0
        AND parent_reqtype_id IS NULL
    </select>

    <select id="findMaxSortNumberByParentHome" resultType="java.lang.Integer">
        SELECT MAX(sort) FROM requirement_type
        WHERE
        deleted = 0
        AND proj_id = 0
        AND parent_reqtype_id =  #{parentId}
    </select>

    <select id="findByModifiedDateAfterHardly" resultMap="baseMap">
        SELECT
            sa.*
        FROM
            requirement_type sa
        WHERE
            sa.modified_Date &gt;= #{lastModifiedDate}
            AND sa.proj_id =#{CurrentProject}
    </select>
    <select id="findCountByModifiedDateAfterHardly" resultType="java.lang.Long">
        select count(1) from requirement_type o where o.modified_Date &gt;= #{lastModifiedDate}  and o.proj_id =#{CurrentProject}
    </select>

    <update id="updateRequirementTypeModifyDateByProjIds">
        update requirement_type
        set modified_date =#{modifiedDate},version=version+1
        where deleted=0 and can_deleted=0 and name=#{name}
        and proj_id in
        <foreach collection="projIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="findAllWctypeOnOneProjectHardly" resultMap="baseMap">
        select rt.* from requirement_type rt where rt.proj_id=(select min(proj_id) from sys_project) and rt.wc_type is not null
    </select>

    <select id="selectByIdHardly" resultMap="baseMap">
        SELECT *
        FROM requirement_type
        WHERE reqtype_id = #{id}
        <if test="CurrentProject != 0">
            AND proj_id = #{CurrentProject}
        </if>
    </select>

    <select id="findParentAllByProjId" resultMap="baseMap">
        SELECT
	          *
        FROM
            requirement_type
        WHERE
            proj_id =#{projId}
        AND deleted = 0
        AND parent_reqtype_id IS NULL
        AND(wc_type IS NULL
        <if test="reqTypeSources != null">
            OR wc_type in
            <foreach collection="reqTypeSources" item="sources" open="(" close=")" separator=",">
                #{sources}
            </foreach>
        </if>
        )
        ORDER BY
            sort
    </select>

    <select id="findParentAllByProjIdAndType" resultMap="baseMap">
        SELECT
        *
        FROM
        requirement_type
        WHERE
        proj_id =#{projId}
        AND deleted = 0
        <if test="reqType != null and reqType.length >0">
            AND reqtype_id IN
            <foreach collection="reqType" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        ORDER BY
        sort
    </select>
    <select id="findRequirementTypeDataDTO"
            resultType="cn.facilityone.fm.maintain.report.dto.RequirementTypeDataDTO">
        SELECT
            t.reqtype_id AS id ,
            0 AS num ,
            t.parent_reqtype_id AS pid
        FROM
            requirement_type t
        WHERE
            t.deleted = 0
        AND t.proj_id = #{projId}
    </select>
    <select id="findAllByProjId" resultMap="baseMap">
        SELECT
            *
        FROM
            requirement_type
        WHERE
            proj_id = #{projId}
        AND deleted = 0
        ORDER BY
            sort
    </select>
    <select id="findRequirementTypeMonthDataDTO"
            resultType="cn.facilityone.fm.maintain.report.dto.RequirementTypeDataDTO">
        SELECT
        t.reqtype_id AS id ,
        s.num AS num ,
        t.parent_reqtype_id AS pid
        FROM
        requirement_type t
        LEFT JOIN(
        SELECT
        s.reqtype_id ,
        count(s.req_id) AS num
        FROM
        requirement s
        WHERE
        YEAR(s.created_date) = #{year}
        AND MONTH(s.created_date) = #{month}
        <if test="nowMonth">
            AND DAY(s.created_date) <![CDATA[ < ]]> #{day}
        </if>
        AND s.deleted = 0
        AND s.proj_id = #{projId}
        GROUP BY
        s.reqtype_id
        ) s ON s.reqtype_id = t.reqtype_id
        WHERE
        t.proj_id = #{projId}
        AND t.deleted = 0
    </select>
    <select id="selectTable" resultMap="baseMap">
        select * from requirement_type
        where 1=1
        and proj_id = #{CurrentProject}
        and deleted = 0
        <if test="search.fullName != null and search.fullName != ''">
            AND name LIKE concat('%',#{search.fullName},'%')
        </if>
        <choose>
            <when test="search.reqTypes != null and search.reqTypes.size() > 0">
                AND
                        (
                        wc_type in
                <foreach collection="search.reqTypes" item="reqTypes" open="(" close=")" separator=",">
                    #{reqTypes}
                </foreach>
                or wc_type is null
                        )
            </when>
            <otherwise>
                AND wc_type is null
            </otherwise>
        </choose>
    </select>
    <select id="selectByIdAndProject" resultMap="baseMap">
        SELECT *
        FROM requirement_type
        WHERE reqtype_id = #{id}
        AND proj_id = #{projId}
    </select>

    <select id="findReqTypeIdByTeamId" resultType="java.lang.Long">
        SELECT requirement_type.reqtype_id
        FROM
        requirement_type
        INNER JOIN requirement_type_wo_team ON requirement_type_wo_team.reqtype_id = requirement_type.reqtype_id
        WHERE	requirement_type_wo_team.work_team_id in
        <foreach collection="teamIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND requirement_type.deleted = 0
    </select>

    <select id="findOthersParentRequirementTypeByNameHardly" resultMap="baseMap">
        SELECT
            reqtype_id AS id
        FROM
            requirement_type rt
        WHERE
            rt.`name` = #{name}
        AND rt.parent_reqtype_id IS NULL
        AND rt.reqtype_id != #{tid}
        AND rt.deleted = 0
        AND rt.proj_id = 0
    </select>
    <select id="findParentRequirementTypeByNameHardly" resultMap="baseMap">
        SELECT
            reqtype_id AS id
        FROM
            requirement_type rt
        WHERE
            rt.`name` = #{name}
        AND rt.parent_reqtype_id IS NULL
        AND rt.deleted = 0
        AND rt.proj_id = 0
    </select>
    <select id="findOthersRequirementTypeByNameAndParentIdHardly" resultMap="baseMap">
        SELECT
            reqtype_id AS id
        FROM
            requirement_type rt
        WHERE
            rt.`name` = #{name}
        AND rt.parent_reqtype_id = #{pid}
        AND rt.reqtype_id != #{tid}
        AND rt.deleted = 0
        AND rt.proj_id = 0
    </select>

    <select id="findRequirementTypeByNameAndParentIdHardly" resultMap="baseMap">
        SELECT
            reqtype_id AS id
        FROM
            requirement_type rt
        WHERE
            rt.`name` = #{name}
        AND rt.parent_reqtype_id = #{pid}
        AND rt.deleted = 0
        AND rt.proj_id = 0
    </select>

    <select id="findAllOderSortDesc" resultMap="baseMap">
        SELECT
            *
        FROM
            requirement_type rt
        WHERE
            rt.deleted = 0
        AND rt.deleted = 0
        AND rt.proj_id = 0
        <if test="CurrentStandard !=null ">
            AND rt.standard_id = #{CurrentStandard}
        </if>
        ORDER BY
            rt.sort DESC ,
            rt.reqtype_id ASC
    </select>

    <select id="findH5RequirmentType"
            resultType="cn.facilityone.fm.maintain.wechat.h5.dto.H5RequirementTypeDTO">
        SELECT type.reqtype_id          AS id,
               type.NAME                AS 'name',
               type.full_name           AS fullName,
               type.wc_type             AS wcType,
               type.sort                AS sort,
               type.can_deleted         AS canDeleted,
               type.parent_reqtype_id   AS parentId,
               setting.location_bool    AS locationBool,
               setting.description_bool AS descriptionBool
        FROM requirement_type type
                 INNER JOIN requirement_type_setting setting ON type.reqtype_id = setting.requirement_type_id
        WHERE type.deleted = 0
          AND type.proj_id = #{projectId}
          AND setting.h5_visable = 1
    </select>

    <select id="remindTable"
            resultType="cn.facilityone.fm.maintain.servicecenter.dto.RequirementTypeRemindDTO">
        SELECT
            rni.*,
            GROUP_CONCAT(DISTINCT rt.`name`) AS requirementTypesName
        FROM remind_node_item rni
        LEFT JOIN remind_node_business rnb ON rni.id = rnb.remind_node_item_id
        LEFT JOIN remind_node_notifier rnnWt ON rnnWt.remind_node_item_id = rni.id AND rnnWt.type = 'workTeam'
        LEFT JOIN work_team wt ON wt.work_team_id = rnnWt.notifier
        LEFT JOIN remind_node_notifier rnnEm ON rnnEm.remind_node_item_id = rni.id AND rnnEm.type = 'employee'
        LEFT JOIN em em ON em.em_id = rnnEm.notifier
        LEFT JOIN remind_node_notifier rnnEmail ON rnnEmail.remind_node_item_id = rni.id AND rnnEmail.type = 'email'
        LEFT JOIN requirement_type rt ON rt.reqtype_id = rnb.business_id AND rnb.business_type = CONCAT('RequirementType','_',#{conditions.type}) AND rt.deleted = 0
        WHERE rni.deleted = 0
        AND rni.type = #{conditions.type} AND rni.proj_id = #{CurrentProject}
        <if test="conditions.module != null and conditions.module != ''">
            AND rni.module = #{conditions.module}
        </if>
        <if test="conditions.name != null and conditions.name != ''">
            AND rni.name LIKE CONCAT('%',#{conditions.name},'%')
        </if>
        <if test="conditions.workTeamsName != null and conditions.workTeamsName != ''">
            AND wt.work_team_name LIKE CONCAT('%',#{conditions.workTeamsName},'%')
        </if>
        <if test="conditions.employeesName != null and conditions.employeesName != ''">
            AND em.em_name LIKE CONCAT('%',#{conditions.employeesName},'%')
        </if>
        <if test="conditions.emailsName != null and conditions.emailsName != ''">
            AND rnnEmail.notifier LIKE CONCAT('%',#{conditions.emailsName},'%')
        </if>
        <if test="conditions.requirementTypesName != null and conditions.requirementTypesName != ''">
            AND rt.`name` LIKE CONCAT('%',#{conditions.requirementTypesName},'%')
        </if>
        <if test="conditions.msgType != null and conditions.msgType != ''">
            AND rni.msg_type LIKE CONCAT('%',#{conditions.msgType},'%')
        </if>
        <if test="conditions.id_notin != null">
            AND rni.id NOT IN
            <foreach collection="conditions.id_notin" item="item"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="conditions.customized != null">
            <choose>
                <when test="conditions.customized == 'true'">
                    AND rni.customized = 1
                </when>
                <when test="conditions.customized == 'false'">
                    AND rni.customized = 0
                </when>
            </choose>
        </if>
        <if test="conditions.enable != null">
            <choose>
                <when test="conditions.enable == 'true'">
                    AND rni.enable = 1
                </when>
                <when test="conditions.enable == 'false'">
                    AND rni.enable = 0
                </when>
            </choose>
        </if>
        GROUP BY rni.id
    </select>
</mapper>