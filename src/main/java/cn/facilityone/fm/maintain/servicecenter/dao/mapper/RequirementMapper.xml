<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.facilityone.fm.maintain.servicecenter.dao.RequirementMapper">

    <insert id="insertRequirementFiles">
        INSERT requirement_file (requirement_id,fm_file_hash) VALUES
        <foreach collection="fileHashs" item="hash" separator=",">
            (#{id},#{hash})
        </foreach>
    </insert>

    <delete id="clearRequirementAttachmentAndPictureFiles">
        DELETE requirement_file
        FROM requirement_file
                 LEFT JOIN fm_file ON fm_file.HASH = requirement_file.fm_file_hash
        WHERE fm_file.type IN ('attachment', 'image')
          AND requirement_id = #{id}
    </delete>


    <select id="selectPageByCondition"
            resultMap="RequirementBaseMap">
        SELECT
        <include refid="RequirementBaseFieldSQL"/>
        FROM requirement
        LEFT JOIN requirement_type ON requirement_type.reqtype_id = requirement.reqtype_id
        LEFT JOIN requirement_type_wo_team ON requirement_type_wo_team.reqtype_id = requirement_type.reqtype_id
        LEFT JOIN customer ON customer.cust_id = requirement.cust_id
        WHERE requirement.deleted = 0
        AND requirement.proj_id = #{CurrentProject}
        <if test="condition.workTeamIds != null">
            AND ( requirement_type_wo_team.work_team_id IN
            <foreach collection="condition.workTeamIds" item="teamId" open="(" close=")" separator=",">
                #{teamId}
            </foreach>
            or requirement_type_wo_team.work_team_id is null
            )
        </if>
        <if test="condition.queryKey != null and condition.queryKey != ''">
            AND (requirement.request_name LIKE concat('%',#{condition.queryKey}, '%')
            OR requirement.request_code LIKE concat('%', #{condition.queryKey}, '%')
            OR requirement.description LIKE concat('%', #{condition.queryKey}, '%')
            OR requirement.contact_phone LIKE concat('%', #{condition.queryKey}, '%')
            OR requirement_type.name LIKE concat('%', #{condition.queryKey}, '%')
            OR customer.cust_name LIKE concat('%', #{condition.queryKey}, '%')
            OR customer.cust_title LIKE concat('%', #{condition.queryKey}, '%')
            OR customer.mobile LIKE concat('%',#{condition.queryKey}, '%')
            OR customer.phone LIKE concat('%', #{condition.queryKey}, '%')
            )
        </if>
        <if test="condition.createTimeStartTime != null and condition.createTimeEndTime != null">
            AND requirement.created_date BETWEEN #{condition.createTimeStartTime} AND #{condition.createTimeEndTime}
        </if>
        <if test="condition.comTimeStartTime != null and condition.comTimeEndTime != null">
            AND requirement.completed_datetime BETWEEN #{condition.comTimeStartTime} AND #{condition.comTimeEndTime}
        </if>
        <if test="condition.status != null">
            AND requirement.status IN
            <foreach collection="condition.status" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="condition.sources != null">
            AND requirement.source IN
            <foreach collection="condition.sources" item="source" open="(" close=")" separator=",">
                #{source}
            </foreach>
        </if>
        GROUP BY requirement.req_id
        ORDER BY requirement.req_id DESC
    </select>

    <select id="selectRequirementFileById" resultType="java.lang.String">
        SELECT DISTINCT fm_file_hash
        FROM requirement_file
        WHERE requirement_id = #{id}
    </select>

    <select id="selectDemandInfo" resultType="cn.facilityone.fm.maintain.servicecenter.dto.CustomerDTO">
        SELECT em.em_id AS 'employeeId',
        NULL AS 'customerId',
        em.em_name AS 'requestName',
        CONCAT_WS( '/', IF ( em.extension IS NOT NULL AND em.extension != '', em.extension, NULL ),
        IF ( em.phone IS NOT NULL AND em.phone != '', em.phone, NULL ) )  AS 'requestLink',
        em.em_name AS 'nameCompany',
        em.email AS 'emailAddress',
        em.org_id AS 'orgId',
        em.site_id AS 'siteId',
        em.bl_id AS 'buildingId',
        em.fl_id AS 'floorId',
        em.rm_id AS 'roomId'
        FROM em
        WHERE em.deleted = 0 AND em.activated = 1 AND em.proj_id = #{CurrentProject}
        <if test="queryString != null and queryString != ''">
            AND em.em_name LIKE concat('%', #{queryString}, '%')
        </if>
        UNION
        SELECT
        NULL AS 'employeeId' ,
        customer.cust_id AS 'customerId' ,
        customer.cust_name AS 'requestName',
        CONCAT_WS( '/', IF ( customer.phone IS NOT NULL AND customer.phone != '', customer.phone, NULL ),
        IF ( customer.mobile IS NOT NULL AND customer.mobile != '', customer.mobile, NULL ) )  AS 'requestLink',
        CONCAT(customer.cust_name, ' , ', customer.company) AS 'nameCompany',
        customer.email AS 'emailAddress',
        NULL AS 'orgId',
        NULL AS 'siteId',
        NULL AS 'buildingId',
        NULL AS 'floorId',
        NULL AS 'roomId'
        FROM customer
        WHERE customer.deleted = 0 AND customer.proj_id = #{CurrentProject}
        <if test="queryString != null and queryString != ''">
            AND customer.cust_name LIKE concat('%', #{queryString}, '%')
        </if>
    </select>

    <select id="selectFileHashsById" resultType="java.lang.String">
        SELECT DISTINCT requirement_file.fm_file_hash
        FROM requirement_file
                 LEFT JOIN fm_file ON fm_file.hash = requirement_file.fm_file_hash
        WHERE fm_file.type = #{fileType}
          AND requirement_id = #{id}
    </select>

    <select id="selectRequirementTable"
            resultType="cn.facilityone.fm.maintain.servicecenter.dto.RequirementColumnDTO">
        SELECT DISTINCT
        requirement.req_id AS 'id',
        requirement.request_code AS 'code',
        requirement.request_name AS 'requestName',
        requirement_type.full_name AS 'requirementType.fullName',
        requirement.description AS 'description',
        requirement.resert_start_time as 'reserStartTime',
        requirement.resert_end_time as 'reserEndTime',
        requirement.source as 'source',
        requirement.completed_datetime as 'completedDateTime',
        b.bl_name as buildingName,
        r.rm_name as roomName,
        f.fl_name as floorName,
        lease_info.name as tenant,
        lease_info.brand_name as brandName,
        requirement.status AS 'status',
        evaluation.quality AS 'evaluation.quality',
        evaluation.speed AS 'evaluation.speed',
        evaluation.attitude AS 'evaluation.attitude',
        evaluation.description AS 'evaluation.description',
        GROUP_CONCAT(wo.wo_code) AS 'workOrderCode',
        requirement.created_date AS 'createdDate',
        sys_project.proj_name AS projectName
        FROM requirement
        left join lease_info on lease_info.id=requirement.lease_info_id
        LEFT JOIN requirement_type ON requirement_type.reqtype_id = requirement.reqtype_id
        LEFT JOIN evaluation ON evaluation.id = requirement.evaluation_id
        LEFT JOIN customer ON customer.cust_id = requirement.cust_id
        LEFT JOIN wo ON wo.req_id = requirement.req_id
        LEFT JOIN geo_building b on b.bl_id=requirement.bl_id
        LEFT JOIN geo_floor f on f.fl_id=requirement.fl_id
        LEFT JOIN geo_room r on r.rm_id = requirement.rm_id
        INNER JOIN sys_project on sys_project.proj_id = requirement.proj_id and sys_project.deleted = 0
        WHERE requirement.deleted = 0
        <choose>
            <when test="condition.home != null and condition.home">
                <choose>
                    <when test="condition.projectId_in != null and condition.projectId_in.size() > 0">
                        AND requirement.proj_id in
                        <foreach collection="condition.projectId_in" item="projectId" open="(" close=")"
                                 separator=",">
                            #{projectId}
                        </foreach>
                    </when>
                    <otherwise>
                        <if test="CurrentProjects != null and CurrentProjects.size() > 0 and CurrentProjects.get(0) != 0">
                            AND requirement.proj_id in
                            <foreach collection="CurrentProjects" item="currentProject" open="(" close=")"
                                     separator=",">
                                #{currentProject}
                            </foreach>
                        </if>
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                AND requirement.proj_id = #{CurrentProject}
            </otherwise>
        </choose>
        <if test="column.projectName != null and column.projectName != ''">
            AND sys_project.proj_name LIKE concat('%', #{column.projectName}, '%')
        </if>
        <if test="column.code != null and column.code != ''">
            AND requirement.request_code LIKE concat('%', #{column.code}, '%')
        </if>
        <if test="column.code != null and column.code != ''">
            AND requirement.request_code LIKE concat('%', #{column.code}, '%')
        </if>
        <if test="column.requestName != null and column.requestName != ''">
            AND requirement.request_name LIKE concat('%', #{column.requestName}, '%')
        </if>
        <if test="column.requirementType_fullName != null and column.requirementType_fullName != ''">
            AND requirement_type.name LIKE concat('%', #{column.requirementType_fullName}, '%')
        </if>
        <if test="column.description != null and column.description != ''">
            AND requirement.description LIKE concat('%', #{column.description}, '%')
        </if>
        <if test="column.status != null">
            AND requirement.status = #{column.status}
        </if>
        <if test="column.evaluation_quality != null">
            AND evaluation.quality <![CDATA[ >= ]]> #{column.evaluation_quality}
        </if>
        <if test="column.evaluation_speed != null">
            AND evaluation.speed <![CDATA[ >= ]]> #{column.evaluation_speed}
        </if>
        <if test="column.evaluation_attitude != null">
            AND evaluation.attitude <![CDATA[ >= ]]> #{column.evaluation_attitude}
        </if>
        <if test="column.evaluation_description!= null">
            AND evaluation.description LIKE concat('%', #{column.evaluation_description}, '%')
        </if>
        <if test="column.workOrderCode != null and column.workOrderCode != ''">
            AND wo.wo_code LIKE concat('%', #{column.workOrderCode}, '%')
        </if>
        <if test="column.createdDate != null and column.createdDate.startDate != null and column.createdDate.endDate != null">
            AND requirement.created_date BETWEEN #{column.createdDate.startDate} AND
            #{column.createdDate.endDate}
        </if>
        <if test="condition.isLinkWo != null">
            AND (CASE WHEN wo.req_id IS NULL THEN 0 ELSE 1 END) = #{condition.isLinkWo}
        </if>
        <if test="condition.createdDate != null and condition.createdDate.startDate != null and condition.createdDate.endDate != null">
            AND requirement.created_date BETWEEN #{condition.createdDate.startDate} AND
            #{condition.createdDate.endDate}
        </if>
        <if test="condition.completedDateTime != null and condition.completedDateTime.startDate != null and condition.completedDateTime.endDate != null">
            AND requirement.completed_datetime BETWEEN #{condition.completedDateTime.startDate}
            AND #{condition.completedDateTime.endDate}
        </if>
        <if test="condition.status != null">
            AND requirement.status IN
            <foreach collection="condition.status" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="condition.source != null">
            AND requirement.source IN
            <foreach collection="condition.source" item="source" open="(" close=")" separator=",">
                #{source}
            </foreach>
        </if>
        <if test="condition.evaluationMethod_in != null">
            AND evaluation.evaluation_method IN
            <foreach collection="condition.evaluationMethod_in" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        GROUP BY requirement.req_id
        <if test="order != null">
            ORDER BY 1=1 ASC
            <if test="order.code != null and order.code != ''">
                ,requirement.request_code ${order.code}
            </if>
            <if test="order.createdDate != null and order.createdDate != ''">
                ,requirement.created_date ${order.createdDate}
            </if>
        </if>
    </select>

    <select id="selectRequirementTableForAi"
            resultType="cn.facilityone.fm.maintain.servicecenter.dto.RequirementColumnDTO">
        SELECT DISTINCT
        requirement.req_id AS 'id',
        requirement.request_code AS 'code',
        requirement.request_name AS 'requestName',
        requirement_type.full_name AS 'requirementType.fullName',
        requirement.description AS 'description',
        requirement.resert_start_time as 'reserStartTime',
        requirement.resert_end_time as 'reserEndTime',
        requirement.source as 'source',
        requirement.completed_datetime as 'completedDateTime',
        b.bl_name as buildingName,
        r.rm_name as roomName,
        f.fl_name as floorName,
        lease_info.name as tenant,
        lease_info.brand_name as brandName,
        requirement.status AS 'status',
        evaluation.quality AS 'evaluation.quality',
        evaluation.speed AS 'evaluation.speed',
        evaluation.attitude AS 'evaluation.attitude',
        evaluation.description AS 'evaluation.description',
        GROUP_CONCAT(wo.wo_code) AS 'workOrderCode',
        requirement.created_date AS 'createdDate',
        sys_project.proj_name AS projectName
        FROM requirement
        left join lease_info on lease_info.id=requirement.lease_info_id
        LEFT JOIN requirement_type ON requirement_type.reqtype_id = requirement.reqtype_id
        LEFT JOIN evaluation ON evaluation.id = requirement.evaluation_id
        LEFT JOIN customer ON customer.cust_id = requirement.cust_id
        LEFT JOIN wo ON wo.req_id = requirement.req_id
        LEFT JOIN geo_building b on b.bl_id=requirement.bl_id
        LEFT JOIN geo_floor f on f.fl_id=requirement.fl_id
        LEFT JOIN geo_room r on r.rm_id = requirement.rm_id
        INNER JOIN sys_project on sys_project.proj_id = requirement.proj_id and sys_project.deleted = 0
        WHERE requirement.deleted = 0
        <choose>
            <when test="condition.home != null and condition.home">
                <choose>
                    <when test="condition.projectId_in != null and condition.projectId_in.size() > 0">
                        AND requirement.proj_id in
                        <foreach collection="condition.projectId_in" item="projectId" open="(" close=")"
                                 separator=",">
                            #{projectId}
                        </foreach>
                    </when>
                    <otherwise>
                        <if test="CurrentProjects != null and CurrentProjects.size() > 0 and CurrentProjects.get(0) != 0">
                            AND requirement.proj_id in
                            <foreach collection="CurrentProjects" item="currentProject" open="(" close=")"
                                     separator=",">
                                #{currentProject}
                            </foreach>
                        </if>
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                AND requirement.proj_id = #{CurrentProject}
            </otherwise>
        </choose>
        <if test="column.projectName != null and column.projectName != ''">
            AND sys_project.proj_name LIKE concat('%', #{column.projectName}, '%')
        </if>
        <if test="column.code != null and column.code != ''">
            AND requirement.request_code LIKE concat('%', #{column.code}, '%')
        </if>
        <if test="column.code != null and column.code != ''">
            AND requirement.request_code LIKE concat('%', #{column.code}, '%')
        </if>
        <if test="column.requestName != null and column.requestName != ''">
            AND requirement.request_name LIKE concat('%', #{column.requestName}, '%')
        </if>
        <if test="column.requirementType_fullName != null and column.requirementType_fullName != ''">
            AND requirement_type.name LIKE concat('%', #{column.requirementType_fullName}, '%')
        </if>
        <if test="column.description != null and column.description != ''">
            AND requirement.description LIKE concat('%', #{column.description}, '%')
        </if>
        <if test="column.status != null">
            AND requirement.status = #{column.status}
        </if>
        <if test="column.evaluation_quality != null">
            AND evaluation.quality <![CDATA[ >= ]]> #{column.evaluation_quality}
        </if>
        <if test="column.evaluation_speed != null">
            AND evaluation.speed <![CDATA[ >= ]]> #{column.evaluation_speed}
        </if>
        <if test="column.evaluation_attitude != null">
            AND evaluation.attitude <![CDATA[ >= ]]> #{column.evaluation_attitude}
        </if>
        <if test="column.evaluation_description!= null">
            AND evaluation.description LIKE concat('%', #{column.evaluation_description}, '%')
        </if>
        <if test="column.workOrderCode != null and column.workOrderCode != ''">
            AND wo.wo_code LIKE concat('%', #{column.workOrderCode}, '%')
        </if>
        <if test="column.createdDate != null and column.createdDate.startDate != null and column.createdDate.endDate != null">
            AND requirement.created_date BETWEEN #{column.createdDate.startDate} AND
            #{column.createdDate.endDate}
        </if>
        <if test="condition.isLinkWo != null">
            AND (CASE WHEN wo.req_id IS NULL THEN 0 ELSE 1 END) = #{condition.isLinkWo}
        </if>
        <if test="condition.createdDate != null and condition.createdDate.startDate != null and condition.createdDate.endDate != null">
            AND requirement.created_date BETWEEN #{condition.createdDate.startDate} AND
            #{condition.createdDate.endDate}
        </if>
        <if test="condition.completedDateTime != null and condition.completedDateTime.startDate != null and condition.completedDateTime.endDate != null">
            AND requirement.completed_datetime BETWEEN #{condition.completedDateTime.startDate}
            AND #{condition.completedDateTime.endDate}
        </if>
        <if test="condition.status != null">
            AND requirement.status IN
            <foreach collection="condition.status" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="condition.emId != null">
            AND requirement.em_id = #{condition.emId}
        </if>
        <if test="condition.wechatOpenId != null">
            AND requirement.wechat_requester_id = #{condition.wechatOpenId}
        </if>
        <if test="condition.source != null">
            AND requirement.source IN
            <foreach collection="condition.source" item="source" open="(" close=")" separator=",">
                #{source}
            </foreach>
        </if>
        <if test="condition.evaluationMethod_in != null">
            AND evaluation.evaluation_method IN
            <foreach collection="condition.evaluationMethod_in" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        GROUP BY requirement.req_id
        <if test="order != null">
            ORDER BY 1=1 ASC
            <if test="order.code != null and order.code != ''">
                ,requirement.request_code ${order.code}
            </if>
            <if test="order.createdDate != null and order.createdDate != ''">
                ,requirement.created_date ${order.createdDate}
            </if>
        </if>
    </select>

    <select id="selectStatusNums" resultType="java.util.Map">
        SELECT requirement.status, count(DISTINCT requirement.req_id)
        FROM requirement
        WHERE requirement.deleted = 0
          AND requirement.proj_id = #{CurrentProject}
        GROUP BY requirement.status
    </select>

    <select id="selectSourceNums" resultType="java.util.Map">
        SELECT requirement.source, count(DISTINCT requirement.req_id)
        FROM requirement
        WHERE requirement.deleted = 0
          AND requirement.proj_id = #{CurrentProject}
        GROUP BY requirement.source
    </select>

    <resultMap id="RequirementBaseMap" type="cn.facilityone.fm.maintain.servicecenter.entity.Requirement">
        <id column="req_id" property="id" jdbcType="BIGINT"/>
        <result column="request_code" property="code" jdbcType="VARCHAR"/>
        <result column="request_name" property="requestName" jdbcType="VARCHAR"/>
        <result column="contact_phone" property="contactPhone" jdbcType="VARCHAR"/>
        <result column="location" property="location" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="wechat_requester_id" property="wechatRequesterId" jdbcType="VARCHAR"/>
        <result column="customer_key" property="customerKey" jdbcType="VARCHAR"/>
        <result column="request_type" property="requestType" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="source" property="source" jdbcType="INTEGER"/>
        <result column="completed_datetime" property="completedDateTime" jdbcType="TIMESTAMP"/>
        <result column="resert_start_time" property="reserStartTime" jdbcType="TIMESTAMP"/>
        <result column="resert_end_time" property="reserEndTime" jdbcType="TIMESTAMP"/>
        <result column="created_date" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="proj_id" property="project" jdbcType="BIGINT"/>
        <result column="deleted" property="deleted" jdbcType="BOOLEAN"/>
        <result column="site_id" property="siteId" jdbcType="BIGINT"/>
        <result column="bl_id" property="buildingId" jdbcType="BIGINT"/>
        <result column="fl_id" property="floorId" jdbcType="BIGINT"/>
        <result column="rm_id" property="roomId" jdbcType="BIGINT"/>
        <result column="eq_id" property="eqId" jdbcType="BIGINT"/>
        <result column="cust_id" property="customerId" jdbcType="BIGINT"/>
        <result column="em_id" property="emId" jdbcType="BIGINT"/>
        <result column="reqtype_id" property="reqtypeId" jdbcType="BIGINT"/>
        <result column="follow_up_id" property="followUpId" jdbcType="BIGINT"/>
        <result column="evaluation_id" property="evaluationId" jdbcType="BIGINT"/>
        <result column="created_date" property="createdDate"/>
        <result column="resert_start_time" property="reserStartTime"/>
        <result column="resert_end_time" property="reserEndTime"/>
        <result column="lease_info_id" property="leaseInfoId"/>
        <association property="requirementType" columnPrefix="type_"
                     javaType="cn.facilityone.fm.maintain.servicecenter.entity.RequirementType">
            <id column="id" property="id"></id>
            <result column="full_name" property="fullName"></result>
            <result column="parent_reqtype_id" property="parentReqtypeId"></result>
            <result column="name" property="name"></result>
            <result column="description" property="description"></result>
            <result column="sort" property="sort"></result>
        </association>
    </resultMap>

    <sql id="RequirementBaseFieldSQL">
        requirement.req_id,
        requirement.request_code,
        requirement.request_name,
        requirement.contact_phone,
        requirement.location,
        requirement.email,
        requirement.description,
        requirement.wechat_requester_id,
        requirement.customer_key,
        requirement.request_type,
        requirement.status,
        requirement.source,
        requirement.completed_datetime,
        requirement.resert_start_time,
        requirement.resert_end_time,
        requirement.proj_id,
        requirement.deleted,
        requirement.site_id,
        requirement.bl_id,
        requirement.fl_id,
        requirement.rm_id,
        requirement.eq_id,
        requirement.reqtype_id,
        requirement.cust_id,
        requirement.em_id,
        requirement.follow_up_id,
        requirement.evaluation_id,
        requirement.created_date,
        requirement_type.reqtype_id AS 'type_id',
        requirement_type.name AS 'type_name',
        requirement_type.full_name AS 'type_full_name',
        requirement_type.description AS 'type_description',
        requirement_type.gen_wo AS 'type_gen_wo',
        requirement_type.stype_id AS 'type_stype_id',
        requirement_type.parent_reqtype_id AS 'type_parent_reqtype_id',
        requirement.lease_info_id
    </sql>

    <select id="findByWorkOrderId" resultMap="RequirementBaseMap">
        SELECT
        <include refid="RequirementBaseFieldSQL"/>
        FROM
        requirement
        INNER JOIN wo ON requirement.req_id = wo.req_id
        left join requirement_type requirement_type on requirement_type.reqtype_id=requirement.reqtype_id
        WHERE
        wo.wo_id = #{woId}
    </select>

    <select id="findRequirement"
            resultType="cn.facilityone.fm.maintain.servicecenter.mobile.dto.MReqQueryResponse">
        SELECT
        DISTINCT
        req.req_id AS reqId ,
        req.request_code AS code ,
        req.request_name AS requester ,
        req.description AS 'desc' ,
        rt.full_name AS type ,
        req.created_date AS createTime ,
        req.status AS status ,
        req.source AS origin,
        req.lease_info_id AS leaseInfoId
        FROM
        requirement req
        LEFT JOIN requirement_type rt ON req.reqtype_id = rt.reqtype_id
        LEFT JOIN requirement_type_wo_team ON requirement_type_wo_team.reqtype_id = rt.reqtype_id
        LEFT JOIN wo ON wo.req_id = req.req_id
        WHERE 1=1
        AND req.deleted = 0
        AND req.proj_id = #{CurrentProject}
        <if test="condition.emId != null">
            AND req.em_id = #{condition.emId}
        </if>
        <if test="condition.status != null">
            AND req.status = #{condition.status}
        </if>
        <if test="condition.value != null and condition.value != ''">
            AND satisfaction_degree.value LIKE concat('%', #{condition.value}, '%')
        </if>
        <if test="condition.workTeamIdList != null and condition.workTeamIdList.size > 0 ">
            AND( requirement_type_wo_team.work_team_id IS NULL
            OR requirement_type_wo_team.work_team_id IN
            <foreach collection="condition.workTeamIdList" item="teamId" open="(" close=")" separator=",">
                #{teamId}
            </foreach>
            )
        </if>
        <if test="condition.interval != null">
            AND req.created_date BETWEEN #{condition.interval.start} AND #{condition.interval.end}
        </if>
        <if test="condition.endTime != null">
            <![CDATA[
            AND req.created_date < #{condition.endTime}
            ]]>
        </if>
        <if test="condition.startTime != null">
            <![CDATA[
            AND req.created_date > #{condition.startTime}
            ]]>
        </if>

        <if test="condition.isLinkWo != null">
            AND (CASE WHEN wo.req_id IS NULL THEN 1 ELSE 0 END) = #{condition.isLinkWo}
        </if>
        <if test="condition.requirementDesc != null and condition.requirementDesc != ''">
            AND req.description LIKE concat('%', #{condition.requirementDesc}, '%')
        </if>
        <if test="condition.requirementCode != null and condition.requirementCode != ''">
            AND req.request_code LIKE concat('%', #{condition.requirementCode}, '%')
        </if>
        <if test="condition.requirementPerson != null and condition.requirementPerson != ''">
            AND req.request_name LIKE concat('%', #{condition.requirementPerson}, '%')
        </if>
        <if test="condition.requirementTypeId != null">
            AND req.reqtype_id =#{condition.requirementTypeId}
        </if>
        <if test="condition.origins != null and condition.origins.size > 0 ">
            AND req.source IN
            <foreach collection="condition.origins" item="source" open="(" close=")" separator=",">
                #{source}
            </foreach>
        </if>
        <if test="condition.finishStartTime != null">
            <![CDATA[
            AND req.completed_datetime > #{condition.finishStartTime}
            ]]>
        </if>
        <if test="condition.finishEndTime != null">
            <![CDATA[
            AND req.completed_datetime < #{condition.finishEndTime}
            ]]>
        </if>
        GROUP BY req.req_id
        ORDER BY req.req_id DESC
    </select>
    <select id="findRequirementDetail"
            resultType="cn.facilityone.fm.maintain.servicecenter.mobile.dto.MReqDetailResponse">
        SELECT
            req.req_id AS reqId ,
            req.request_code AS code ,
            rt.full_name AS type ,
            req.reqtype_id AS typeInteger,
            req.status AS status ,
            req.source AS origin ,
            req.description AS 'desc' ,
            req.created_date AS createTime ,
            req.resert_start_time AS reserveStartDate,
            req.resert_end_time AS reserveEndDate,
            req.contact_phone AS telephone ,
            req.location AS locationName ,
            req.eq_id AS eqId ,
            req.lease_info_id AS leaseInfoId,
            CASE
            WHEN req.em_id IS NOT NULL THEN
                em.user_id
            END AS 'requester.userId' ,
             CASE
            WHEN req.em_id IS NOT NULL THEN
                em.em_name
            WHEN req.cust_id IS NOT NULL THEN
                customer.cust_name
            WHEN req.em_id IS NULL
            AND req.cust_id IS NULL THEN
                req.request_name
            END AS 'requester.name' ,
             CASE
            WHEN req.em_id IS NOT NULL THEN
                posi.pos_name
            WHEN req.cust_id IS NOT NULL THEN
                customer.cust_title
            END AS 'requester.position' ,
             CASE
            WHEN req.cust_id IS NOT NULL THEN
                customer.salutation
            END AS 'requester.appellation' ,
             CASE
            WHEN req.em_id IS NOT NULL THEN
                org.org_name
            WHEN req.cust_id IS NOT NULL THEN
                customer.department
            END AS 'requester.department' ,
             CASE
            WHEN req.em_id IS NOT NULL THEN
                em.phone
            WHEN req.cust_id IS NOT NULL THEN
                customer.mobile
            END AS 'requester.telephone' ,
             CASE
            WHEN req.em_id IS NOT NULL THEN
                em.phone
            WHEN req.cust_id IS NOT NULL THEN
                customer.phone
            END AS 'requester.mobile' ,
             CASE
            WHEN req.em_id IS NOT NULL THEN
                1
            WHEN req.cust_id IS NOT NULL THEN
                2
            WHEN req.em_id IS NULL
            AND req.cust_id IS NULL THEN
                0
            END AS 'requester.type' ,
             s.city_id AS 'location.cityId' ,
             req.site_id AS 'location.siteId' ,
             req.bl_id AS 'location.buildingId' ,
             req.fl_id AS 'location.floorId' ,
             req.rm_id AS 'location.roomId'
            FROM
                requirement req
            LEFT JOIN requirement_type rt ON req.reqtype_id = rt.reqtype_id
            LEFT JOIN em ON req.em_id = em.em_id
            LEFT JOIN org ON em.org_id = org.org_id
            LEFT JOIN position posi ON em.pos_id = posi.pos_id
            LEFT JOIN customer ON req.cust_id = customer.cust_id
            LEFT JOIN geo_site s ON s.site_id = req.site_id
            WHERE 1=1
            AND req.deleted = 0
            AND req.req_id = #{reqId}
    </select>

    <select id="findRequirementByCondition" resultType="cn.facilityone.fm.maintain.servicecenter.entity.Requirement">
        SELECT DISTINCT
        requirement.req_id AS 'id',
        requirement.request_code AS 'code',
        requirement_type.name AS 'requirementType.name',
        requirement.description AS 'description',
        evaluation.quality AS 'evaluation.quality',
        evaluation.speed AS 'evaluation.speed',
        evaluation.attitude AS 'evaluation.attitude',
        evaluation.description AS 'evaluation.description',
        requirement.created_date AS 'createdDate',
        requirement.completed_datetime AS 'completedDateTime',
        evaluation.type AS 'evaluation.evaluationType'
        FROM requirement
        LEFT JOIN requirement_type ON requirement_type.reqtype_id = requirement.reqtype_id
        LEFT JOIN evaluation ON evaluation.id = requirement.evaluation_id
        LEFT JOIN customer ON customer.cust_id = requirement.cust_id
        WHERE requirement.deleted = 0
        AND requirement.proj_id = #{CurrentProject}
        <if test="conditionMap.code != null and conditionMap.code != ''">
            AND requirement.request_code LIKE concat('%', #{conditionMap.code}, '%')
        </if>
        <if test="conditionMap.requirementType_name != null and conditionMap.requirementType_name != ''">
            AND requirement_type.name LIKE concat('%', #{conditionMap.requirementType_name}, '%')
        </if>
        <if test="conditionMap.description != null and conditionMap.description != ''">
            AND requirement.description LIKE concat('%', #{conditionMap.description}, '%')
        </if>
        <if test="conditionMap.evaluation_quality != null">
            AND evaluation.quality <![CDATA[ >= ]]> #{conditionMap.evaluation_quality}
        </if>
        <if test="conditionMap.evaluation_description != null and conditionMap.evaluation_description != ''">
            AND evaluation.description LIKE concat('%', #{conditionMap.evaluation_description}, '%')
        </if>
        <if test="conditionMap.createdDate != null and conditionMap.createdDate.startDate != null and conditionMap.createdDate.endDate != null">
            AND DATE_FORMAT(requirement.created_date,'%Y-%m-%d') BETWEEN #{conditionMap.createdDate.startDate} AND
            #{conditionMap.createdDate.endDate}
        </if>
        <if test="conditionMap.completedDateTime != null and conditionMap.completedDateTime.startDate != null and conditionMap.completedDateTime.endDate != null">
            AND DATE_FORMAT(requirement.completed_datetime,'%Y-%m-%d') BETWEEN
            #{conditionMap.completedDateTime.startDate}
            AND #{conditionMap.completedDateTime.endDate}
        </if>
        <if test="conditionMap.customer_id != null and conditionMap.customer_id != ''">
            AND customer.cust_id = #{conditionMap.customer_id}
        </if>

        ORDER BY 1=1 asc
        <if test="order.code != null and order.code != ''">
            , requirement.request_code ${order.code}
        </if>
        <if test="order.requirementType_name != null and order.requirementType_name != ''">
            , requirement_type.name ${order.requirementType_name}
        </if>
        <if test="order.description != null and order.description != ''">
            , requirement.description ${order.description}
        </if>
        <if test="order.evaluation_quality != null">
            , evaluation.quality ${order.evaluation_quality}
        </if>
        <if test="order.evaluation_evaluationType != null and order.evaluation_evaluationType != ''">
            , evaluation.type ${order.evaluation_evaluationType}
        </if>

        <if test="order.evaluation_description != null and order.evaluation_description != ''">
            , evaluation.description ${order.evaluation_description}
        </if>
        <if test="order.createdDate != null and order.createdDate != ''">
            , requirement.created_date ${order.createdDate}
        </if>
        <if test="order.completedDateTime != null and order.completedDateTime != ''">
            , requirement.completed_datetime ${order.completedDateTime}
        </if>
    </select>

    <select id="findPageByOpenId" resultMap="RequirementBaseMap">
        SELECT
        <include refid="RequirementBaseFieldSQL"/>
        FROM requirement
        LEFT JOIN requirement_type ON requirement_type.reqtype_id = requirement.reqtype_id
        WHERE requirement.deleted = 0
        AND requirement.wechat_requester_id=#{openId}
        <if test="projectId != null">
            and requirement.proj_id = #{projectId}
        </if>
        ORDER BY requirement.req_id desc
    </select>

    <select id="findfindServiceCenterUserIds" resultType="java.lang.Long">
        SELECT distinct su.id
        FROM sys_user su
        INNER JOIN sys_user_role sur ON su.id = sur.user_id
        left join sys_user_project sup on sup.user_id=su.id
        WHERE sur.role_id IN (
            SELECT srm.role_id
            FROM sys_role_menu srm
                     INNER JOIN sys_menu sm ON srm.menu_id = sm.id
            WHERE sm.link = '/service001'
              AND srm.role_id IN (
                SELECT srm2.role_id
                FROM sys_role_menu srm2
                         INNER JOIN sys_menu sm2 ON srm2.menu_id = sm2.id
                WHERE sm2.link = '/service002')
        )
          AND sup.project_id=#{project}
          AND su.active = 1;
    </select>

    <select id="selectStatusNumsByTeamIds"
            resultType="cn.facilityone.fm.maintain.servicecenter.common.alias.ReqStatusCountView">
        SELECT count(distinct requirement.req_id) AS num,`status`
        FROM requirement
        left join requirement_type ON requirement.reqtype_id = requirement_type.reqtype_id
        left join requirement_type_wo_team ON requirement_type_wo_team.reqtype_id = requirement_type.reqtype_id
        WHERE requirement.deleted = 0
        AND requirement.proj_id = #{CurrentProject}
        <if test="teamIds != null and teamIds.size()> 0">
            AND (requirement_type_wo_team.work_team_id is null
            or requirement_type_wo_team.work_team_id IN
            <foreach collection="teamIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        GROUP BY `status`
    </select>

    <sql id="avgReqResponse">
        FROM requirement req
        LEFT JOIN requirement_activity rac ON rac.requirement_id = req.req_id
        LEFT JOIN requirement_activity rap ON rap.requirement_id = req.req_id
        WHERE req.deleted = 0
        AND req.proj_id = #{CurrentProject}
        <if test="reqSources != null and reqSources.size() > 0">
            AND req.source in
            <foreach collection="reqSources" item="reqSources" open="(" separator="," close=")">
                #{reqSources}
            </foreach>
        </if>
        AND req.created_date <![CDATA[ >= ]]> #{dateTime}
    </sql>

    <select id="findValidReqSources" resultType="java.lang.Integer">
        SELECT distinct req.source
        <include refid="avgReqResponse"/>
    </select>

    <select id="findAvgReqResponse" resultType="java.lang.Integer">
        SELECT AVG(UNIX_TIMESTAMP(rap.created_date) - UNIX_TIMESTAMP(rac.created_date))
        <include refid="avgReqResponse"/>
        AND req.STATUS IN (1, 2, 3)
        AND rac.activity_type = 0
        AND rap.activity_type = 1
    </select>

    <select id="findRequirementInReport" resultMap="RequirementBaseMap">
        SELECT
        <include refid="RequirementBaseFieldSQL"/>
        FROM requirement
        LEFT JOIN requirement_type ON requirement.reqtype_id = requirement_type.reqtype_id
        WHERE requirement.deleted = 0 AND requirement.proj_id = #{CurrentProject}
        <if test="column.status != null">
            AND requirement.status = #{column.status}
        </if>
        <if test="firstDay != null and lastDay != null">
            AND requirement.created_date BETWEEN #{firstDay} AND #{lastDay}
        </if>
    </select>
    <select id="findRequirementDTOInReport" resultType="cn.facilityone.fm.maintain.report.dto.RequirementDTO">
        SELECT
        r.req_id AS 'id' ,
        r.request_code AS 'code' ,
        r.request_name AS 'requestName' ,
        r.description AS 'description' ,
        r. status AS 'status' ,
        r.created_date AS 'createDate' ,
        t.full_name AS 'requirementType' ,
        r.request_name AS 'name'
        FROM
        requirement r
        LEFT JOIN requirement_type t ON t.reqtype_id = r.reqtype_id
        WHERE
        YEAR(r.created_date) = #{year}
        AND MONTH(r.created_date) = #{month}
        AND r.deleted = 0
        AND r.proj_id = #{CurrentProject}
        <if test="ids != null and ids.length > 0">
            AND r.reqtype_id IN
            <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="isNowMonth">
            AND DAY(r.created_date) <![CDATA[ < ]]> #{nowDay}
        </if>
    </select>
    <select id="findRequirementSatisDTOInReport"
            resultType="cn.facilityone.fm.maintain.report.dto.RequirementSatisDTO">
        SELECT
        req.req_id as id,
        req.request_code AS CODE ,
        req.request_name AS requestName ,
        req.description ,
        type.full_name AS requirementType ,
        eval.quality ,
        eval.speed ,
        eval.attitude ,
        eval.description AS evalDesc ,
        req.status ,
        eval.created_date AS createDate
        FROM
        requirement req
        LEFT JOIN requirement_type type ON type.reqtype_id = req.reqtype_id
        LEFT JOIN evaluation eval ON req.req_id=eval.requirement_id
        WHERE
        YEAR(eval.created_date) = #{year}
        <if test="month != 0">
            AND month(eval.created_date) = #{month}
            <if test="conditionParams.isNowMonth">
                AND day(eval.created_date) <![CDATA[ < ]]> #{conditionParams.todayDay}
            </if>
        </if>
        <if test="month == 0">
            AND month(eval.created_date) in
            <foreach collection="conditionParams.reportMonthList" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
            AND eval.created_date <![CDATA[ < ]]> sysdate()
        </if>
        AND req.deleted=0 AND req.proj_id=#{CurrentProject}
        <if test="level != 3">
            <if test="type == 'quality'.toString()">
                AND eval.quality <![CDATA[ >= ]]> #{conditionParams.scoreFrom} AND eval.quality <![CDATA[ <= ]]>
                #{conditionParams.scoreTo}
            </if>
            <if test="type == 'speed'.toString()">
                AND eval.speed <![CDATA[ >= ]]> #{conditionParams.scoreFrom} AND eval.speed <![CDATA[ <= ]]>
                #{conditionParams.scoreTo}
            </if>
            <if test="type == 'attitude'.toString()">
                AND eval.attitude <![CDATA[ >= ]]> #{conditionParams.scoreFrom} AND eval.attitude <![CDATA[ <= ]]>
                #{conditionParams.scoreTo}
            </if>
        </if>
    </select>
    <select id="findRqInDateRangesHardly" resultMap="RequirementBaseMap">
        SELECT
        DISTINCT
        <include refid="RequirementBaseFieldSQL"/>
        FROM
        requirement
        LEFT JOIN requirement_type ON requirement.reqtype_id = requirement_type.reqtype_id
        WHERE
        requirement_type.deleted = 0
        AND requirement.deleted = 0
        AND requirement.created_date <![CDATA[ >= ]]> #{firstDay}
        AND requirement.created_date <![CDATA[ <= ]]> #{lastDay}
        AND requirement.status != #{create}
        AND requirement.status != #{cancel}
        AND requirement.proj_id = #{proId}
        <if test="reqSources != null">
            AND requirement.source in
            <foreach collection="reqSources" item="sources" open="(" close=")" separator=",">
                #{sources}
            </foreach>
        </if>
    </select>
    <select id="findRqCompleteInDateRangesHardly" resultMap="RequirementBaseMap">
        SELECT
        <include refid="RequirementBaseFieldSQL"/>
        FROM
        requirement
        LEFT JOIN requirement_type ON requirement.reqtype_id = requirement_type.reqtype_id
        WHERE
        requirement.deleted = 0
        AND requirement_type.deleted = 0
        AND requirement.created_date <![CDATA[ >= ]]> #{firstDay}
        AND requirement.created_date <![CDATA[ <= ]]> #{lastDay}
        AND requirement.status != #{create}
        AND requirement.status != #{process}
        AND requirement.status != #{cancal}
        AND requirement.proj_id = #{proId}
    </select>
    <select id="getSeconds" resultType="java.lang.Integer">
        SELECT
            TO_SECONDS(rap.created_date) - TO_SECONDS(rac.created_date)
        FROM
            requirement req
        LEFT JOIN requirement_activity rac ON rac.requirement_id = req.req_id
        LEFT JOIN requirement_activity rap ON rap.requirement_id = req.req_id
        WHERE
            req.deleted = 0
        AND req.proj_id = #{projId}
        AND rac.activity_type = #{create}
        AND rap.activity_type = #{check}
        <if test="reqSources != null">
            AND req.source in
            <foreach collection="reqSources" item="sources" open="(" close=")" separator=",">
                #{sources}
            </foreach>
        </if>
        AND req.req_id = #{reqId}
    </select>

    <select id="countSatisfactionReq" resultType="int">
        SELECT
        count(*)
        FROM
        (
        SELECT
        (attitude + quality + speed) AS num
        FROM
        requirement req
        LEFT JOIN evaluation e ON req.evaluation_id = e.id
        WHERE
        req.deleted = 0
        AND e.deleted = 0
        AND req.proj_id IN
        <foreach collection="projIds" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND <![CDATA[ req.created_date >=  #{start} and  req.created_date < #{end}]]>
        ) a
        WHERE
        a.num >= 21
    </select>

    <select id="countEvaluateReq" resultType="int">
        SELECT
        count(req.req_id)
        FROM
        requirement req
        LEFT JOIN evaluation e ON req.evaluation_id = e.id
        WHERE
        req.deleted = 0
        AND e.deleted = 0
        AND req.`status` = 3
        AND req.proj_id IN
        <foreach collection="projIds" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND <![CDATA[ req.created_date >=  #{start} and  req.created_date < #{end}]]>
    </select>

    <select id="countAvgResponseTime" resultType="java.lang.Double">
        SELECT
        AVG(
        UNIX_TIMESTAMP(rap.created_date) - UNIX_TIMESTAMP(rac.created_date)
        )
        FROM
        requirement req
        LEFT JOIN requirement_activity rac ON rac.requirement_id = req.req_id
        LEFT JOIN requirement_activity rap ON rap.requirement_id = req.req_id
        WHERE
        req.deleted = 0
        AND req.proj_id IN
        <foreach collection="projIds" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND rac.activity_type = #{createType}
        AND rap.activity_type = #{checkType}
        AND req.source = #{wechat}
        AND <![CDATA[ req.created_date >=  #{start} and  req.created_date <  #{end}]]>
    </select>
    <select id="findByCode" resultType="cn.facilityone.fm.maintain.servicecenter.dto.RequirementColumnsDTOV2">
        SELECT
		requirement.req_id AS 'id',
		requirement.request_code AS 'code',
		requirement.request_name AS 'requestName',
		requirement_type.full_name AS 'requirementTypeFullName',
		requirement.description AS 'description',
		requirement.status AS 'status',
		GROUP_CONCAT(wo.wo_code) AS 'workOrderCode',
		requirement.created_date AS 'createdDate'
		FROM requirement
		LEFT JOIN requirement_type ON requirement_type.reqtype_id = requirement.reqtype_id
		LEFT JOIN wo ON wo.req_id = requirement.req_id
		WHERE requirement.deleted = 0
		AND requirement.request_code = #{code}
        <if test="projectId != null and projectId != 0">
		and requirement.proj_id = #{projectId}
        </if>
    </select>

    <select id="findCompletedRequirementsByAfterDate"
            resultType="cn.facilityone.fm.maintain.servicecenter.entity.Requirement">
        SELECT
            r.req_id AS "id",
	        r.request_code AS code,
	        r.*
        FROM
            requirement r
        WHERE
            completed_datetime <![CDATA[ < ]]> #{date}
          AND r.proj_id = #{projectId}
          AND r.deleted = FALSE
          AND r.status = 2
    </select>

    <select id="selectCountH5" resultType="java.lang.Integer">
        SELECT count(r.req_id)
        FROM requirement r
        LEFT JOIN  requirement_type rt ON r.reqtype_id = rt.reqtype_id
        WHERE rt.wc_type=6
        and r.proj_id=#{projectId}
    </select>
    <select id="getReportGroupByProj" resultType="cn.facilityone.fm.maintain.report.dto.RequiredMonthDTO">
        SELECT
            MONTH(r.created_date) AS month,
            r.proj_id as projId,
            COUNT(r.req_id) AS value
        FROM
            requirement r
            INNER JOIN sys_project p ON r.proj_id = p.proj_id
        WHERE
            YEAR(r.created_date) = #{year}
        AND r.deleted = 0
        AND p.deleted = 0
        <if test="projects != null and projects.size > 0">
            AND r.proj_id in
            <foreach collection="projects" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY
            MONTH(r.created_date),
        r.proj_id
    </select>
    <select id="findMinYearMonthReportByProjs"
            resultType="cn.facilityone.fm.maintain.report.view.YearMonthReportView">
        SELECT
            MIN(
                    YEAR ( created_date )) AS year,
	MIN(
	MONTH ( created_date )) AS month
        FROM
            requirement
        WHERE
            created_date IS NOT NULL
            and deleted = 0
        <if test="projects != null and projects.size > 0">
            AND proj_id in
            <foreach collection="projects" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectRequirementStatus" resultType="cn.facilityone.fm.maintain.servicecenter.dto.StatusNumberDTO">
        select status,count(*) as 'num' from (
        SELECT DISTINCT
        requirement.req_id AS 'id',
        requirement.request_code AS 'code',
        requirement.request_name AS 'requestName',
        requirement_type.full_name AS 'requirementType.fullName',
        requirement.description AS 'description',
        requirement.resert_start_time as 'reserStartTime',
        requirement.resert_end_time as 'reserEndTime',
        requirement.source as 'source',
        requirement.location as 'location',
        requirement.status AS 'status',
        evaluation.quality AS 'evaluation.quality',
        evaluation.speed AS 'evaluation.speed',
        evaluation.attitude AS 'evaluation.attitude',
        evaluation.description AS 'evaluation.description',
        GROUP_CONCAT(wo.wo_code) AS 'workOrderCode',
        requirement.created_date AS 'createdDate',
        sys_project.proj_name AS projectName
        FROM requirement
        LEFT JOIN requirement_type ON requirement_type.reqtype_id = requirement.reqtype_id
        LEFT JOIN evaluation ON evaluation.id = requirement.evaluation_id
        LEFT JOIN customer ON customer.cust_id = requirement.cust_id
        LEFT JOIN wo ON wo.req_id = requirement.req_id
        INNER JOIN sys_project on sys_project.proj_id = requirement.proj_id and sys_project.deleted = 0
        WHERE requirement.deleted = 0
        <choose>
            <when test="condition.home != null and condition.home">
                <choose>
                    <when test="condition.projectId_in != null and condition.projectId_in.size() > 0">
                        AND requirement.proj_id in
                        <foreach collection="condition.projectId_in" item="projectId" open="(" close=")"
                                 separator=",">
                            #{projectId}
                        </foreach>
                    </when>
                    <otherwise>
                        <if test="CurrentProjects != null and CurrentProjects.size() > 0 and CurrentProjects.get(0) != 0">
                            AND requirement.proj_id in
                            <foreach collection="CurrentProjects" item="currentProject" open="(" close=")"
                                     separator=",">
                                #{currentProject}
                            </foreach>
                        </if>
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                AND requirement.proj_id = #{CurrentProject}
            </otherwise>
        </choose>
        <if test="column.projectName != null and column.projectName != ''">
            AND sys_project.proj_name LIKE concat('%', #{column.projectName}, '%')
        </if>
        <if test="column.code != null and column.code != ''">
            AND requirement.request_code LIKE concat('%', #{column.code}, '%')
        </if>
        <if test="column.code != null and column.code != ''">
            AND requirement.request_code LIKE concat('%', #{column.code}, '%')
        </if>
        <if test="column.requestName != null and column.requestName != ''">
            AND requirement.request_name LIKE concat('%', #{column.requestName}, '%')
        </if>
        <if test="column.requirementType_fullName != null and column.requirementType_fullName != ''">
            AND requirement_type.name LIKE concat('%', #{column.requirementType_fullName}, '%')
        </if>
        <if test="column.description != null and column.description != ''">
            AND requirement.description LIKE concat('%', #{column.description}, '%')
        </if>
        <if test="column.status != null">
            AND requirement.status = #{column.status}
        </if>
        <if test="column.evaluation_quality != null">
            AND evaluation.quality <![CDATA[ >= ]]> #{column.evaluation_quality}
        </if>
        <if test="column.evaluation_speed != null">
            AND evaluation.speed <![CDATA[ >= ]]> #{column.evaluation_speed}
        </if>
        <if test="column.evaluation_attitude != null">
            AND evaluation.attitude <![CDATA[ >= ]]> #{column.evaluation_attitude}
        </if>
        <if test="column.evaluation_description!= null">
            AND evaluation.description LIKE concat('%', #{column.evaluation_description}, '%')
        </if>
        <if test="column.workOrderCode != null and column.workOrderCode != ''">
            AND wo.wo_code LIKE concat('%', #{column.workOrderCode}, '%')
        </if>
        <if test="column.createdDate != null and column.createdDate.startDate != null and column.createdDate.endDate != null">
            AND DATE_FORMAT(requirement.created_date,'%Y-%m-%d') BETWEEN #{column.createdDate.startDate} AND
            #{column.createdDate.endDate}
        </if>
        <if test="condition.isLinkWo != null">
            AND (CASE WHEN wo.req_id IS NULL THEN 0 ELSE 1 END) = #{condition.isLinkWo}
        </if>
        <if test="condition.createdDate != null and condition.createdDate.startDate != null and condition.createdDate.endDate != null">
            AND DATE_FORMAT(requirement.created_date,'%Y-%m-%d') BETWEEN #{condition.createdDate.startDate} AND
            #{condition.createdDate.endDate}
        </if>
        <if test="condition.completedDateTime != null and condition.completedDateTime.startDate != null and condition.completedDateTime.endDate != null">
            AND DATE_FORMAT(requirement.completed_datetime,'%Y-%m-%d') BETWEEN #{condition.completedDateTime.startDate}
            AND #{condition.completedDateTime.endDate}
        </if>
        <if test="condition.status != null">
            AND requirement.status IN
            <foreach collection="condition.status" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="condition.source != null">
            AND requirement.source IN
            <foreach collection="condition.source" item="source" open="(" close=")" separator=",">
                #{source}
            </foreach>
        </if>
        <if test="condition.evaluationMethod_in != null">
            AND evaluation.evaluation_method IN
            <foreach collection="condition.evaluationMethod_in" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        GROUP BY requirement.req_id ) a  group by status
    </select>

</mapper>