package cn.facilityone.fm.maintain.ai.dto;

import lombok.Data;

/**
 * AI工单导出专用DTO
 *
 * <AUTHOR>
 * @Date 2025/8/27
 */
@Data
public class AiWorkOrderExportDTO {
    /**
     * 搜索人ID
     */
    private Long userId;

    /**
     * 匹配的项目ID
     */
    private Long projectId;

    /**
     * 开始日期
     * 格式：YYYY-MM-DD，例如：2025-01-01
     */
    private String startDate;

    /**
     * 结束日期
     * 格式：YYYY-MM-DD，例如：2025-01-31
     */
    private String endDate;

    /**
     * 分页页码（可选，默认1）
     */
    private Integer pageNumber;

    /**
     * 分页大小（可选，默认1000）
     */
    private Integer pageSize;
}
