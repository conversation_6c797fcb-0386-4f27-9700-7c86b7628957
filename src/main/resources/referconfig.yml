# tomcat
server:
  port: 23456
  servlet:
    context-path: /f1-shang
  tomcat:
    max-http-form-post-size: 10MB
    max-connections: 10000
    accesslog:
      enabled: false
      rotate: true
      rename-on-rotate: true
      # 日志地址要改的
      directory: ${fm-log.home}/${fm-log.module}/tomcat
    threads:
      max: 200
      min-spare: 10

#logging config: classpath:logback-spring.xml
fm-log:
  # 日志地址要改的
  home: ${user.dir}/logs
  module: maintian
  console-enabled: true
  console-level: ERROR
  file-debug-enabled: false
  file-warn-enabled: false
  file-error-enabled: true
  file-monitor-enabled: true
  file-max-size: 10MB
  file-max-history: 168
  file-monitor-max-history: 7

# fm system custom properties
fm-core:
  mvc:
    login: /login
    index: /main_v2/index?current_locale=LOCALE#__aurl=%2Fchart%2Fdashboard%2Findex_new
    dingtalk: /static/dd.html
  i18n:
    support-locales: zh_C<PERSON>,en_US,zh_HK
    system-language: zh_CN
  persistence:
    global-param-enabled: true
  security:
    check-login: true
    check-permission: true
    check-password-regex: true
    check-captcha: false
    check-password: false
    check-password-error-count: false
    client-cookie-name: cid
    expire-password-error-count: 120
    max-password-error-count: 10
    check-auth: true
    token-expire: 7200
    token-extra-expire: 60
  monitor:
    enabled: true
    json-pretty: false
    full-class-name: false
  message:
    debug: false
    threads: 1
    thread-sleep-interval: 1
    callback-threads: 1
    callback-thread-sleep-interval: 10
    web-socket-on: true
    site:
      enabled: true
      hostname:
      chanel: DefaultChannel
      port: 9901
      keystore:
      keystorepwd:
      corePoolSize: 5
      threadNamePrefix: threadpool-site-
    email:
      enabled: true
      corePoolSize: 5
      threadNamePrefix: threadpool-email-
    mobile:
      enabled: true
      maxAccount: 500
      # 1 表示生产环境  2 表示测试环境
      environment: 2
      # 最好每个客户改一下
      serverId: maintain
      # 如下四个 每个配置都不同
      aliPushAndroidAppKey: ********
      aliPushIosAppKey: *********
      aliAccessKey: LTAI083vKXr9anPs
      aliAccessSecret: wExto9ZLo1DEMaSRQJ13EVeGJS8QUp
      aliAndroidActivity: com.facilityone.wireless.a.message.PopupPushActivity
      corePoolSize: 5
      threadNamePrefix: threadpool-mobile-
    sms:
      # 短信收费 默认不配置
      enabled: true
      aliAccessKeyId: LTAIKD67IKISkIHW
      aliAccessSecret: SHHcELNusUi2rlwK2piMpGZxneOltz
      aliSignName: FacilityOne
      defaultTemplateCode: SMS_156775045
  file:
    generate-thumbnail: false
    check-file-access-login: true
    access-login-filter-pattern: /files/*
    mapped-static-files:  # pattern | path
      - /** | classpath:/static/
      - /favicon.ico | classpath:/static/favicon.ico
      - /files/** | file:${user.dir}/upload/
      - /wechat/files/** | file:${user.dir}/upload/
      - /app/files/** | file:${user.dir}/upload/
      - /dalian/** | file:D:\\dist\\dalian\\
    upload-path: ${user.dir}/upload/
    upload-static-path: /files/
    allowed-suffix: txt,doc,docx,xls,xlsx,csv,ppt,pptx,pdf,gif,jpg,png,ico,svg,swf,dwg,mp3,mp4,flv,rmvb,rar,zip,jpeg,bmp
  threadpool:
    maxPoolSize: 50
    corePoolSize: 10
    keepAliveTime: 10
    queueCapacity: 60
    threadNamePrefix: threadpool-default-


# xxl-job
xxl:
  job:
    enabled: true
    # job要改的
    ip: *************
    port: 9998
    app-name: fm2-maintain
    log-path: ${user.dir}/logs/xxl-job/
    log-retention-days: 10
    access-token: kid
    admin-addresses: http://*************:2222/xxl-job-admin

# kisso
kisso:
  config:
    signkey: Janfv5UgKhoDrH73EZT7m+81pgqLN3EjWKXZtqF9lQHH9WruxqX0+FkQys6XK0QXzSUckseOAZGeQyvfreA3tw==
    cookieName: maintainid
    cookieMaxage: 15552000 #180days

# jetcache redis配置要改的，有两个地方
jetcache:
  areaInCacheName: false
  statIntervalMinutes: 5
  local:
    default:
      type: caffeine
      keyConvertor: fastjson
      limit: 100
  remote: # if redis requires password, uri = redis://:111111@**************:6379/
    default:
      keyPrefix: fm2_maintain_
      type: redis.lettuce
      keyConvertor: fastjson
      uri: redis://:111111@localhost:6379/4

# mybatis-plus
# [https://mp.baomidou.com/guide/wrapper.html#abstractwrapper]
# [https://mp.baomidou.com/guide/wrapper.html#querywrapper]
# [https://mp.baomidou.com/guide/wrapper.html#updatewrapper]
mybatis-plus:
  check-config-location: true
  mapper-locations: classpath*:cn/facilityone/fm/**/mapper/*.xml
  type-aliases-package: cn.facilityone.fm.**.entity
  global-config:
    db-config:
      logic-delete-value: 1
      logic-not-delete-value: 0
    refresh: true
  plugin:
    performance:
      open: false
      maxTime: 100
  configuration:
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler

# spring flyway
# spring datasource 数据库配置要改的
db:
  host: localhost
  port: 3306
  name: fm-cluster-coe-test
  param: useUnicode=true&characterEncoding=UTF-8&serverTimezone=GMT%2B8&useSSL=false&allowMultiQueries=true&sessionVariables=group_concat_max_len=102400
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${db.host}:${db.port}/${db.name}?${db.param}
    username: root
    password: root
    hikari:
      maximum-pool-size: 100
      minimum-idle: 10
  messages:
    basename: i18n.message,i18n.entity,i18n.page
    encoding: UTF-8
  flyway: # flyway version format : *******
    enabled: true
    out-of-order: true
    clean-disabled: true
    check-location: true
    placeholder-replacement: false
    ignore-missing-migrations: true
    validate-on-migrate: false
    baseline-on-migrate: true
    baseline-version: *******
    table: flyway_db
    locations: classpath:db/migration, classpath:cn.facilityone.fm.core.migration, cn.facilityone.fm.maintain.migration
    installed-by: FacilityONE
  mail:
    host: smtp.exmail.qq.com
    port: 465
    username: <EMAIL>
    password: Fone151009
    properties:
      from: FMSystem<<EMAIL>>
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
          ssl:
            enable: true
  servlet:
    multipart:
      enabled: true
      max-file-size: 50MB
      max-request-size: 1000MB
  freemarker:
    enabled: true
    check-template-location: false
    template-loader-path: classpath:/static/
    suffix: .ftl
  redis:
    host: localhost
    port: 6379
    database: 4
    password: 111111

# xia & shang properties from origin fm system
xia:
  core:
    environment: develop
    base-static-path: /f1-shang
    domainname: http://localhost:23456

shang:
  system:
    not-show-menus:
    wechat-menus: 2400,2700
    zh-name: F-ONE
    en-name: F-ONE
    phoneValid: ^((13[0-9])|(14[5-9])|(15[^4,\D])|(17[0-8])|(18[0-9])|(19[8,9])|166)\d{8}$
  company:
    name: F-ONE
  patrol:
    task:
      hot:
        data:
          months: 6
  rapid:
    implementation: true

#============================================================================
# Configure Pay 缴费模块，没有则不用配置，默认不配置
#============================================================================
pay:
  ali:
    start: true
    pid: 2088721451422945
    appid: 2016080400168717
    alipay-public-key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAumLzxk4EMchQ8m7lNBocXOv3xfaGioLtNNu9esAagHQU0C0RCQN/iMxdAOAt5bjzAAOuLpjyuGM7YqiFQi80POn0vKzMhq9MGmkXfT3/gLsKQCzOyyYqVOxwloMISwLKqpJpd/hi/W1H1MD51DwTFRkF2n916FIwnwjF48os3RdtLxfFWL93fFLMdftjlC5php6kJmorMSASZHi91xbjagtX/C4mQ+0QY61d19MEMt0MMekY/VN9k7pNW5x9XbBsBY3/1czj+ma8VnzO1HlH7Oe4QdbF74zJRRYBYtJ6UXlKmwcqj09OS+2VF3Bh1ltBf8ymujr2VccO88liFFGyKQIDAQAB
  wx:
    start: true
    app-id: wx9d7221cc936ad396
    api-secret: wx9d7221cc936ad396fmoneapisecret
    mchId: 1293179401
    sub-mch-id: wx9d7221cc936ad396
    cert-path: classpath:pay/wxpay/apiclient_cert.p12
  start: true
  # SANDBOX | PRODUCT
  environment: SANDBOX
  domain: http://wechat.wuxi.fmone.cn
  ip: *************
  title-prefix: 费哲物业服务收费
  custom: FONE
  money: 3.01

# minio 文件服务器
minio:
  remote:
    enabled: true
    url: http://minio.fmone.cn
    accessKey: minioadmin
    secretKey: minioadmin
    bucketName: fm2
    tempDir: temp/
    domainUrl: http://localhost:23456/f1-shang

#coe配置（跳转、互通）
coe:
  enabled: false
  prefix: http://*************:8081/f1-coe
  suffix: /openapi/v1
  domain: http://*************:8081/#/portalJumpPage
  access-token: vtUc1m4pgSwcUKHzkT
  access-key: qEz1ZUDD7BmqdChR14IC12cxU1Io
  pageSize: 500
  # 是否同步部门
  syncDepartment: false

#二装
ez:
  projId: 1

#事故报告 附件配置
accreport:
  # 事故报告邮件中附件的格式，默认word
  att: word

#事故报告如果格式要求pdf 则需安装插件 配置位置
jodconverter:
  local:
    enabled: true
    # libreOffice根目录
    officeHome: C:\Program Files\LibreOffice
    portNumber: 8100
