<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta charset="utf-8">
    <title>${title}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1, user-scalable=no">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="shortcut icon" href="${baseStaticPath}/favicon.ico"/>
    <link rel="bookmark" href="${baseStaticPath}/favicon.ico"/>
    <!-- STYLESHEETS --><!--[if lt IE 9]>
    <script src="http://html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <script src="http://css3-mediaqueries-js.googlecode.com/svn/trunk/css3-mediaqueries.js"></script><![endif]-->
<#--<link rel="stylesheet" type="text/css" href="/resource/css/cloud-admin.css">-->
    <link rel="stylesheet" type="text/css" href="${baseStaticPath}/resource/js/js-plugin/bootstrap/css/bootstrap.min.css">
<#--<link rel="stylesheet" type="text/css" href="${baseStaticPath}/resource/css/login_temp.css">-->
    <link rel="stylesheet" type="text/css" href="${baseStaticPath}/resource/css/login_new.css">
    <link rel="stylesheet" type="text/css" href="${baseStaticPath}/resource/css/plugin/font-awesome/css/font-awesome.min.css">
    <script>var PUBLIC_PATH = '${baseStaticPath}';</script>
</head>
<body>

<div class="login">
    <div class="login__background"></div>

    <!--右侧边栏-->
    <div class="login__form">

        <!--切换语言-->
        <div class="login__form-lan">
                <span class="login__form-lan-span" id="loginLanguage">
                    <span id="login_language">简体中文</span><img style="margin: 0 0 2px 6px;" src="${baseStaticPath}/resource/img/common/icon_arrow_more.png">
                </span>
            <ul class="nav-dropdown" id="nav-dropdown">
                <li id="login_choose_zh" class="nav-dropdown__item">简体中文</li>
                <li id="login_choose_en" class="nav-dropdown__item">English</li>
                <li id="login_choose_HK" class="nav-dropdown__item">繁體中文</li>
                <div class="popper__arrow"></div>
            </ul>
        </div>

        <!--form表格-->
        <div class="login__form-info">
            <div class="login__form-logo">
                <img src="${baseStaticPath}/resource/img/facilityone.png">
            </div>
            <div id="login-form">
                <!--用户名-->
                <div class="login__form-info-group">
                        <span class="login__form-info-icon">
                            <img src="${baseStaticPath}/resource/img/common/icon_account.png">
                        </span>
                    <input type="text" name="username" id="username" placeholder="账号"
                           class="login__form-info-input">
                </div>
                <!--密码-->
                <div class="login__form-info-group">
                        <span class="login__form-info-icon">
                            <img src="${baseStaticPath}/resource/img/common/icon_password.png">
                        </span>
                    <input type="password" id="password" name="password" placeholder="密码"
                           class="login__form-info-input">
                    <input type="hidden" name="client_id" value="${client_id}">
                    <input type="hidden" name="response_type" value="${response_type}">
                    <input type="hidden" name="redirect_uri" value="${redirect_uri}">
                </div>
                <input type="hidden" name="ucperson_language" id="ucperson_language" value="zh_CN">
                <div class="login__form-group">
                    <button class="login__form-info-submit" id="login_btn">
                        <span id="login_btn_span">登录</span>
                        <span class="login__form-group-arrow">
                            <img src="${baseStaticPath}/resource/img/common/icon_arrow_right.png">
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="loginModal" data-backdrop="static" data-keyboard="false" tabindex="-1"
         aria-labelledby="staticBackdropLabel" aria-hidden="false">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="staticBackdropLabel" style="font-weight: 600;">服务协议及及隐私政策</h3>
                    <button type="button" class="closeX" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div></div>
                <div class="modal-body" id="modal-body" style="height: 600px;overflow-y: auto;">
                    <div class="agreementswipper" id="agreementswipper">
                        <div id="contentZh" style="display: none">
                        <p>法律声明</p>
                        <p><br/>
                            一、特别提示</p>
                        <p>在使用本软件前，请您务必仔细阅读并全面理解本声明，特别是本声明粗体部分。您一旦使用本软件，您的使用行为将被视为对本声明全部内容的认可，且本声明任何条款均不应视为格式条款。</p>
                        <p><br/>
                            二、用户使用须知</p>
                        <p>
                            1、用户使用本软件，必须遵守包括《中华人民共和国刑法》、《中华人民共和国计算机信息网络国际联网管理暂行规定》、《互联网信息服务管理办法》等在内的现有互联网法律法规。用户或其发布的相关文章、图片、评论、广告、宣传、活动及其他各项信息均由其依法承担全部责任。</p>

                        <p>2、用户无权实施包括但不限于下列行为：</p>

                        <p>2.1、删除软件及其他副本上所有关于版权的信息、内容；</p>

                        <p>2.2、对软件进行反向工程、反向编译或反向汇编，以及改动编译程序文件内部的任何资源；</p>

                        <p>2.3、未经本公司书面同意，使用、复制、修改、链接、转载、汇编、发表、出版软件相关信息等，建立镜像站点、擅自借助软件发展与之有关的衍生产品、作品、服务、插件、外挂、兼容、互联等；</p>

                        <p>
                            2.4、进行危害计算机网络安全的行为，包括但不限于：使用未经许可的数据或进入未经许可的服务器；未经许可对计算机信息网络功能进行删除、修改或者增加；未经允许对计算机信息网络中存储、处理或者传输的数据和应用程序进行删除、修改或者增加；未经许可企图探查、扫描、测试本软件系统或网络的弱点或其它实施破坏计算机信息网络安全的行为；企图干涉、破坏本软件系统正常运行，故意传播恶意程序或病毒以及其他破坏干扰正常网络信息服务的行为；</p>

                        <p>
                            2.5、将软件提供的服务用于核设施运行、生命维持或其他会使人类及其财产处于危险之中的重大设备。用户明白本软件提供的服务并非为以上目的而设计，如果因为软件和服务的原因导致以上操作失败而带来的人员伤亡、严重的财产损失和环境破坏，本公司将不承担任何责任；</p>

                        <p>
                            2.6、在未经本公司书面明确授权前提下，出售、出租、出借、散布、转移或转授权软件和服务或相关的链接或从使用软件和服务或软件和服务的条款中获利，无论以上使用是否为直接经济或金钱收益；</p>

                        <p>2.7、其他以任何不合法的方式、为任何不合法的目的、或以任何与本协议不一致的方式使用软件提供的服务。</p>

                        <p>
                            2.8、软件的替换、修改和升级：本公司保留在任何时候通过为您提供本软件替换、修改、升级版本的权利以及为替换、修改或升级收取费用的权利。本公司保留因业务发展需要，单方面对软件的部分功能效果进行改变或进行限制，用户需承担此风险。</p>

                        <p>
                            2.9、非经本公司或本公司授权开发并正式发布的其它任何由软件衍生的软件均属非法，下载、安装、使用此类软件，将可能导致不可预知的风险，建议用户不要轻易下载、安装、使用，由此产生的一切法律责任与纠纷一概与本公司无关。</p>

                        <p>
                            2.10、对于从非本公司指定站点下载的软件产品以及从非本公司发行的介质上获得的软件产品，本公司无法保证该软件是否感染计算机病毒、是否隐藏有伪装的木马程序或者黑客软件，使用此类软件，将可能导致不可预测的风险，建议用户不要轻易下载、安装、使用，本公司不承担任何由此产生的一切法律责任。</p>

                        <p>
                            2.11、使用软件必须遵守国家有关法律和政策等，维护国家利益，保护国家安全，并遵守本协议，对于用户违法或违反本协议的使用而引起的一切责任，由用户负全部责任，一概与本公司及合作单位无关，导致本公司及合作单位损失的，本公司及合作单位有权要求用户赔偿，并有权立即停止向其提供服务。</p>

                        <p><br/>
                            三、软件知识产权特别声明</p>

                        <p>
                            1、官网的软件由本公司开发。软件的一切版权等知识产权，以及与软件相关的所有信息内容，包括但不限于：文字表述及其组合、图标、图饰、图表、色彩、界面设计、版面框架、有关数据、印刷材料、或电子文档等均受著作权法和国际著作权条约以及其他知识产权法律法规的保护。</p>

                        <p>2、未经本公司书面同意，用户不得为任何营利性或非营利性的目的自行实施、利用、转让或许可任何第三方实施、利用、转让上述知识产权，本公司保留追究上述未经许可行为的权利。</p>

                        <p>
                            3、用户可以为非商业目的在单一终端设备上安装、使用、显示、运行我们的软件。用户不得为商业运营目的安装、使用、运行软件，不可以对该软件或者该软件运行过程中释放到任何计算机终端内存中的数据及该软件运行过程中客户端与服务器端的交互数据进行复制、更改、修改、挂接运行或创作任何衍生作品，形式包括但不限于使用插件、外挂或非经授权的第三方工具/服务接入软件和相关系统。</p>

                        <p>4、保留权利：未明示授权的其他一切权利仍归本公司所有，用户使用其他权利时须另外取得本公司的书面同意。</p>

                        <p><br/>
                            四、关于隐私权保护</p>

                        <p>
                            1、为了进一步完善您在使用软件过程中的用户体验，向您提供更好的服务，我们会在为您提供服务的过程中收集必要的数据信息，并通过分析这些数据，为您提供更升级、贴心的服务。例如，帮助我们了解用户使用软件和服务时遇到的问题，以改善软件和服务的质量、性能和安全性。</p>

                        <p>2、本公司高度重视用户信息保护。</p>

                        <p>3、用户同意，在以下（包括但不限于）几种情况下，本公司有权使用用户的信息资源：</p>

                        <p>3.1、执行软件验证或升级服务；</p>

                        <p>3.2、提高用户的使用安全性并提供客户支持；</p>

                        <p>3.3、改善或提高软件的技术和服务，例如，帮助我们了解用户使用软件和服务时遇到的问题，帮助我们改善软件和服务的质量、性能和安全性；</p>

                        <p>3.4、为用户发送通知和软件提供的服务信息；</p>

                        <p>3.5、本公司可能会与第三方合作向用户提供相关的网络服务所必须，且该第三方同意承担与本公司同等的保护用户隐私的责任；</p>

                        <p>3.6、在不透露单个用户隐私资料的前提下，本公司可能对用户数据库进行分析并生成统计数据；</p>

                        <p>3.7、其他有利于用户利益和实现本软件功能的使用行为。</p>

                        <p>4、本公司将会采取合理的措施保护用户信息，不向第三方公开、透露用户信息，除以下情形之外：</p>

                        <p>4.1、基于法律或法律赋予权限的政府部门要求；</p>

                        <p>4.2、在紧急情况下，为维护用户及公众的权益；</p>

                        <p>4.3、为维护本公司的商标权、专利权及其他任何合法权益；</p>

                        <p>4.4、用户同意公开用户信息的。</p>

                        <p>5、因用户使用第三方服务或者设备，可能导致用户信息通过其他方式透露给第三方，用户需自行了解第三方对用户信息保护的相关条款，本公司不承担由此产生的风险。</p>

                        <p>6、用户可以选择不向本公司提供用户信息，或者根据产品设置取消软件收集某些信息。因用户未向本公司提供信息导致相关服务功能无法实现，本公司不为此承担责任。</p>

                        <p><br/>
                            五、法律责任与免责</p>

                        <p>1、用户认可本公司软件将会尽一切合理努力以保护用户的计算机资源及计算机通讯的隐私性和完整性，但是用户承认和同意本公司不能就此事提供任何承诺或保证。</p>

                        <p>2、本公司特别提请用户注意，为了保障软件发展和调整的自主权，本公司拥有随时自行修改或中断软件授权而不需通知用户的权利，如有必要，修改或中断会以通告形式公布于系统重要页面上。</p>

                        <p>
                            3、用户违反本声明或相关的服务条款的规定，导致或产生的任何第三方主张的任何索赔、要求或损失，包括合理的律师费，用户同意赔偿本公司与合作公司、关联公司，并使之免受损害。对此，本公司有权视用户的行为性质，采取包括但不限于中断使用许可、停止提供服务、限制使用、法律追究等措施。</p>

                        <p>
                            4、软件会经过详细的测试，但不能保证与所有的软硬件系统完全兼容，不能保证软件完全没有错误。如果出现不兼容及错误的情况，用户可将情况报告本公司，获得技术支持。如果无法解决兼容性问题，用户可以删除本软件。</p>

                        <p>
                            5、在适用法律允许的最大范围内，对因使用或不能使用软件所产生的损害及风险，包括但不限于直接或间接的个人损害、商业赢利的丧失、贸易中断、商业信息的丢失或任何其它经济损失，本公司不承担任何责任。</p>

                        <p>
                            6、对于因不能控制的原因（含系统升级和维护、电信系统或互联网网络故障、计算机故障、计算机系统问题）或其它任何不可抗力原因而造成的网络服务中断、数据丢失或其他缺陷，本公司不承担任何责任。</p>

                        <p>7、用户违反本声明，对本公司造成损害的，本公司有权采取包括但不限于中断使用许可、停止提供服务、限制使用、法律追究等措施。</p>

                        <p><br/>
                            六、其他条款</p>

                        <p>1、本协议所定的任何条款的部分或全部无效者，不影响其它条款的效力。</p>

                        <p>
                            2、本协议的解释、效力及纠纷的解决，适用于中华人民共和国法律。若用户和本公司之间发生任何纠纷或争议，首先应友好协商解决，协商不成的，用户在此完全同意将纠纷或争议提交本公司所在地的人民法院管辖。</p>
                        </div>
                        <div id="contentEn"  style="display: none">
                            <p>Legal Notice</p><br/>
                            <p>I. Special Notice</p>
                            <p>Before using this software, please make sure you read and fully understand this statement,
                                especially the bolded part of this statement. Once you use this software, your use will be
                                regarded as the recognition of all the contents of this statement, and no provision of this
                                statement shall be regarded as a form clause.</p><br/>
                            <p>Second, the user use instructions</p>
                            <p>1.Users using the Software must comply with existing Internet laws and regulations, including
                                the "Criminal Law of the People's Republic of China", "Interim Provisions of the People's
                                Republic of China on the Management of Computer Information Network International
                                Networking", "Internet Information Services Management Measures", etc. Users or their
                                release of relevant articles, pictures, comments, advertisements, publicity, activities and
                                all other information shall be fully responsible for them in accordance with the law.</p>
                            <p>2. Users are not entitled to perform acts including but not limited to the following:</p>
                            <p>2.1. remove all information and content on the software and other copies regarding
                                copyright;</p>
                            <p>2.2. reverse engineering, reverse compiling or disassembling the Software, and altering any
                                resources within the compiled program files;</p>
                            <p>2.3. using, copying, modifying, linking, reprinting, compiling, publishing, publishing
                                software-related information, etc., establishing mirror sites, developing derivative
                                products, works, services, plug-ins, plug-ins, compatibility, interconnection, etc. related
                                to the software without the written consent of the Company</p>
                            <p>2.4. conduct acts that endanger the security of the computer network, including but not
                                limited to: the use of unauthorized data or access to unauthorized servers; unauthorized
                                deletion, modification or addition to the functions of the computer information network;
                                unauthorized deletion, modification or addition to the data and applications stored,
                                processed or transmitted in the computer information network; unauthorized attempts to
                                probe, scan, test the Software system or network weaknesses or other implementation of acts
                                that undermine the security of computer information networks; attempts to interfere with,
                                disrupt the normal operation of the software system, deliberately spread malicious programs
                                or viruses and other acts that undermine interference with normal network information
                                services;</p>
                            <p>2.5. use the services provided by the Software for nuclear facility operation, life support
                                or other major equipment that would put humans and their property at risk. Users understand
                                that the services provided by the Software are not designed for the above purposes and that
                                the Company will not be held responsible for any casualties, serious property damage and
                                environmental damage brought about by the failure of the above operations due to the
                                Software and the Services;</p>
                            <p>2.6. sell, rent, lend, distribute, transfer or sub-license the Software and Services or
                                related links or profit from the use of the Software and Services or the terms of the
                                Software and Services without the express written authorization of the Company, whether or
                                not the above use is for direct economic or monetary gain;</p>
                            <p>2.7. any other use of the Software and Services provided in any unlawful manner, for any
                                unlawful purpose, or in any manner inconsistent with this Agreement.</p>
                            <p>2.8. Replacement, modification and upgrade of the Software: The Company reserves the right at
                                any time to provide you with a replacement, modified or upgraded version of the Software and
                                the right to charge a fee for such replacement, modification or upgrade. The Company
                                reserves the right to unilaterally change or restrict some of the functional effects of the
                                Software due to business development needs, and the user shall bear this risk.</p>
                            <p>2.9. Any other software derived from the Software not developed and officially released by
                                the Company or authorized by the Company is illegal. Downloading, installing and using such
                                software may lead to unpredictable risks, and users are advised not to download, install or
                                use it easily, and all legal responsibilities and disputes arising therefrom have nothing to
                                do with the Company.</p>
                            <p>2.10. For software products downloaded from sites not designated by the Company and software
                                products obtained from media not issued by the Company, the Company cannot guarantee whether
                                the software is infected with computer viruses, hidden Trojan horses or hacker software, and
                                the use of such software may lead to unpredictable risks, so the user is advised not to
                                download, install or use it easily, and the Company shall not assume any legal
                                responsibility for any resulting The company does not assume any legal responsibility
                                arising from this.</p>
                            <p>2.11. The use of the software must comply with relevant national laws and policies, safeguard
                                national interests, protect national security, and comply with this agreement, the user is
                                fully responsible for all responsibilities arising from the use of the user's violation of
                                the law or violation of this agreement, and has nothing to do with the Company and its
                                partners, resulting in losses to the Company and its partners, the Company and its partners
                                have the right to require the user to compensate, and the right to immediately stop The
                                Company and its partners have the right to demand compensation from the user and immediately
                                stop providing services to him/her.</p><br/>
                            <p>III. Special declaration of software intellectual property rights</p>
                            <p>1. The software on the official website is developed by our company. All copyright and other
                                intellectual property rights of the software, as well as all information content related to
                                the software, including but not limited to: text expressions and their combinations, icons,
                                graphics, charts, colors, interface design, layout frames, relevant data, printed materials,
                                or electronic documents are protected by copyright laws and international copyright treaties
                                and other intellectual property laws and regulations.</p>
                            <p>2.Without the written consent of the Company, the user shall not implement, utilize, transfer
                                or license any third party to implement, utilize or transfer the above intellectual property
                                rights for any profit or non-profit purposes, and the Company reserves the right to pursue
                                the above unauthorized acts.</p>
                            <p>3. Users may install, use, display and run our software on a single terminal device for
                                non-commercial purposes. Users may not install, use, or run the software for commercial
                                operation purposes, and may not copy, change, modify, hook up, run, or create any derivative
                                works of the software or the data released into the memory of any computer terminal during
                                the operation of the software and the interaction data between the client and the server
                                during the operation of the software, including but not limited to the use of plug-ins,
                                plug-ins, or non-authorized third-party tools/services to access the software and related
                                systems. Services to access the software and related systems.</p>
                            <p>4.Reserved rights: All other rights not expressly authorized remain with the Company, and
                                users must obtain separate written consent from the Company when using other rights.</p><br/>
                            <p>IV. About privacy protection</p>
                            <p>1. In order to further improve your user experience in using the software and provide you
                                with better services, we will collect the necessary data and information in the process of
                                providing services to you, and through the analysis of these data, we will provide you with
                                more upgraded and intimate services. For example, to help us understand the problems users
                                encounter when using the software and services, in order to improve the quality, performance
                                and security of the software and services.</p>
                            <p>2.The Company attaches great importance to the protection of user information.</p>
                            <p>3.The user agrees that the Company has the right to use the user's information resources
                                under the following (including but not limited to) several circumstances:</p>
                            <p>3.1.Performing software verification or upgrade services;</p>
                            <p>3.2.Improve the user's security of use and provide customer support;</p>
                            <p>3.3. to improve or enhance the technology and services of the software, for example, to help
                                us understand the problems users encounter when using the software and services and to help
                                us improve the quality, performance and security of the software and services</p>
                            <p>3.4. sending notices and information about the services provided by the software to
                                users;</p>
                            <p>3.5. the Company may cooperate with third parties to provide users with the relevant network
                                services necessary, and the third party agrees to assume the same responsibility as the
                                Company to protect the privacy of users</p>
                            <p>3.6. the Company may analyze the user database and generate statistical data without
                                disclosing the privacy information of individual users</p>
                            <p>3.7. other acts of use that are conducive to the interests of the user and the achievement of
                                the functions of the Software.</p>
                            <p>4.The Company will take reasonable measures to protect user information and not to disclose
                                or reveal user information to third parties, except for the following cases:</p>
                            <p>4.1, based on the law or the requirements of the government department that the law gives
                                authority to;</p>
                            <p>4.2. In case of emergency, in order to protect the rights and interests of users and the
                                public;</p>
                            <p>4.3. in order to maintain the Company's trademark rights, patent rights and any other
                                legitimate rights and interests;</p>
                            <p>4.4. Where the user agrees to disclose the user's information.</p>
                            <p>5.The use of third-party services or equipment by the user may result in the disclosure of
                                user information to third parties by other means, the user needs to understand the relevant
                                terms of third-party protection of user information, and the Company does not assume the
                                risks arising therefrom.</p>
                            <p>6.The user can choose not to provide the Company with user information or cancel the
                                collection of certain information by the software according to the product settings. The
                                Company shall not be responsible for the failure of the relevant service functions due to
                                the user's failure to provide information to the Company.</p><br/>
                            <p>V. Legal Liability and Exclusion of Liability</p>
                            <p>1. The user acknowledges that the Company's software will make every reasonable effort to
                                protect the privacy and integrity of the user's computer resources and computer
                                communications, but the user acknowledges and agrees that the Company cannot provide any
                                promises or guarantees in this regard.</p>
                            <p>2. The Company especially draws the user's attention to the fact that in order to protect the
                                autonomy of the software development and adjustment, the Company has the right to modify or
                                discontinue the software license at any time at its own discretion without notice to the
                                user, and if necessary, the modification or discontinuation will be published in the form of
                                a notice on the important pages of the system.</p>
                            <p>3. The user agrees to indemnify and hold harmless the Company and its partner companies and
                                affiliates from any claims, demands or losses, including reasonable attorney's fees,
                                asserted by any third party as a result of or arising out of the user's violation of the
                                provisions of this Statement or the related Terms of Service. In this regard, the Company
                                has the right to take measures including, but not limited to, interrupting the use of the
                                license, stopping the provision of services, restricting the use, and legal prosecution,
                                depending on the nature of the user's behavior.</p>
                            <p>4. The software will be tested in detail, but there is no guarantee that it will be fully
                                compatible with all hardware and software systems, or that the software will be completely
                                error-free. In case of incompatibility and errors, users can report the situation to our
                                company for technical support. If the compatibility problem cannot be solved, the user can
                                delete the software.</p>
                            <p>5.To the maximum extent permitted by applicable law, the Company shall not be liable for
                                damages and risks arising from the use or inability to use the Software, including but not
                                limited to direct or indirect personal damages, loss of business winnings, interruption of
                                trade, loss of business information or any other economic loss.</p>
                            <p>6.The Company shall not be liable for any interruption of network services, loss of data or
                                other defects caused by reasons beyond its control (including system upgrade and
                                maintenance, telecommunications system or Internet network failure, computer failure,
                                computer system problems) or any other force majeure reasons.</p>
                            <p>7. If the user violates this statement and causes damage to the Company, the Company has the
                                right to take measures including, but not limited to, interrupting the use of the license,
                                stopping the provision of services, restricting the use, and pursuing the law.</p><br/>
                            <p>VI. Other Terms</p>
                            <p>1.The invalidity of any part or all of the terms of this Agreement shall not affect the
                                validity of other terms.</p>
                            <p>2.The interpretation, validity and dispute resolution of this Agreement shall be governed by
                                the laws of the People's Republic of China. If any dispute or controversy arises between the
                                user and the Company, it shall first be resolved through friendly consultation, and if
                                consultation fails, the user hereby fully agrees to submit the dispute or controversy to the
                                jurisdiction of the People's Court of the Company's location.</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="agreeTip">
                        <input type="checkbox" class="tip-check" id="tip-check">
                        <p class="tip-text">本人确认已阅读及理解上述政策，并同意遵守</p> <br/>
                        <div class="errorMsg" id="errorMsg" style="display: none">请阅读并勾选同意上述政策</div>
                    </div>
                    <div class="button-group">
                        <button type="button" class="btn btn-cancle" data-dismiss="modal" id="cancle">取消</button>
                        <button type="button" class="btn btn-confirm" id="confirm" disabled>确定</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="loginModal1" data-backdrop="static" data-keyboard="false" tabindex="-1"
         aria-labelledby="staticBackdropLabel" aria-hidden="false">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="staticBackdropLabel" style="font-weight: 600;">Service Agreement and Privacy Policy</h3>
                    <button type="button" class="closeX" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div></div>
                <div class="modal-body" id="modal-body" style="height: 600px;overflow-y: auto;">
                    <div class="agreementswipper" id="agreementswipper">
                        <p>Legal Notice</p><br/>
                        <p>I. Special Notice</p>
                        <p>Before using this software, please make sure you read and fully understand this statement,
                            especially the bolded part of this statement. Once you use this software, your use will be
                            regarded as the recognition of all the contents of this statement, and no provision of this
                            statement shall be regarded as a form clause.</p><br/>
                        <p>Second, the user use instructions</p>
                        <p>1.Users using the Software must comply with existing Internet laws and regulations, including
                            the "Criminal Law of the People's Republic of China", "Interim Provisions of the People's
                            Republic of China on the Management of Computer Information Network International
                            Networking", "Internet Information Services Management Measures", etc. Users or their
                            release of relevant articles, pictures, comments, advertisements, publicity, activities and
                            all other information shall be fully responsible for them in accordance with the law.</p>
                        <p>2. Users are not entitled to perform acts including but not limited to the following:</p>
                        <p>2.1. remove all information and content on the software and other copies regarding
                            copyright;</p>
                        <p>2.2. reverse engineering, reverse compiling or disassembling the Software, and altering any
                            resources within the compiled program files;</p>
                        <p>2.3. using, copying, modifying, linking, reprinting, compiling, publishing, publishing
                            software-related information, etc., establishing mirror sites, developing derivative
                            products, works, services, plug-ins, plug-ins, compatibility, interconnection, etc. related
                            to the software without the written consent of the Company</p>
                        <p>2.4. conduct acts that endanger the security of the computer network, including but not
                            limited to: the use of unauthorized data or access to unauthorized servers; unauthorized
                            deletion, modification or addition to the functions of the computer information network;
                            unauthorized deletion, modification or addition to the data and applications stored,
                            processed or transmitted in the computer information network; unauthorized attempts to
                            probe, scan, test the Software system or network weaknesses or other implementation of acts
                            that undermine the security of computer information networks; attempts to interfere with,
                            disrupt the normal operation of the software system, deliberately spread malicious programs
                            or viruses and other acts that undermine interference with normal network information
                            services;</p>
                        <p>2.5. use the services provided by the Software for nuclear facility operation, life support
                            or other major equipment that would put humans and their property at risk. Users understand
                            that the services provided by the Software are not designed for the above purposes and that
                            the Company will not be held responsible for any casualties, serious property damage and
                            environmental damage brought about by the failure of the above operations due to the
                            Software and the Services;</p>
                        <p>2.6. sell, rent, lend, distribute, transfer or sub-license the Software and Services or
                            related links or profit from the use of the Software and Services or the terms of the
                            Software and Services without the express written authorization of the Company, whether or
                            not the above use is for direct economic or monetary gain;</p>
                        <p>2.7. any other use of the Software and Services provided in any unlawful manner, for any
                            unlawful purpose, or in any manner inconsistent with this Agreement.</p>
                        <p>2.8. Replacement, modification and upgrade of the Software: The Company reserves the right at
                            any time to provide you with a replacement, modified or upgraded version of the Software and
                            the right to charge a fee for such replacement, modification or upgrade. The Company
                            reserves the right to unilaterally change or restrict some of the functional effects of the
                            Software due to business development needs, and the user shall bear this risk.</p>
                        <p>2.9. Any other software derived from the Software not developed and officially released by
                            the Company or authorized by the Company is illegal. Downloading, installing and using such
                            software may lead to unpredictable risks, and users are advised not to download, install or
                            use it easily, and all legal responsibilities and disputes arising therefrom have nothing to
                            do with the Company.</p>
                        <p>2.10. For software products downloaded from sites not designated by the Company and software
                            products obtained from media not issued by the Company, the Company cannot guarantee whether
                            the software is infected with computer viruses, hidden Trojan horses or hacker software, and
                            the use of such software may lead to unpredictable risks, so the user is advised not to
                            download, install or use it easily, and the Company shall not assume any legal
                            responsibility for any resulting The company does not assume any legal responsibility
                            arising from this.</p>
                        <p>2.11. The use of the software must comply with relevant national laws and policies, safeguard
                            national interests, protect national security, and comply with this agreement, the user is
                            fully responsible for all responsibilities arising from the use of the user's violation of
                            the law or violation of this agreement, and has nothing to do with the Company and its
                            partners, resulting in losses to the Company and its partners, the Company and its partners
                            have the right to require the user to compensate, and the right to immediately stop The
                            Company and its partners have the right to demand compensation from the user and immediately
                            stop providing services to him/her.</p><br/>
                        <p>III. Special declaration of software intellectual property rights</p>
                        <p>1. The software on the official website is developed by our company. All copyright and other
                            intellectual property rights of the software, as well as all information content related to
                            the software, including but not limited to: text expressions and their combinations, icons,
                            graphics, charts, colors, interface design, layout frames, relevant data, printed materials,
                            or electronic documents are protected by copyright laws and international copyright treaties
                            and other intellectual property laws and regulations.</p>
                        <p>2.Without the written consent of the Company, the user shall not implement, utilize, transfer
                            or license any third party to implement, utilize or transfer the above intellectual property
                            rights for any profit or non-profit purposes, and the Company reserves the right to pursue
                            the above unauthorized acts.</p>
                        <p>3. Users may install, use, display and run our software on a single terminal device for
                            non-commercial purposes. Users may not install, use, or run the software for commercial
                            operation purposes, and may not copy, change, modify, hook up, run, or create any derivative
                            works of the software or the data released into the memory of any computer terminal during
                            the operation of the software and the interaction data between the client and the server
                            during the operation of the software, including but not limited to the use of plug-ins,
                            plug-ins, or non-authorized third-party tools/services to access the software and related
                            systems. Services to access the software and related systems.</p>
                        <p>4.Reserved rights: All other rights not expressly authorized remain with the Company, and
                            users must obtain separate written consent from the Company when using other rights.</p><br/>
                        <p>IV. About privacy protection</p>
                        <p>1. In order to further improve your user experience in using the software and provide you
                            with better services, we will collect the necessary data and information in the process of
                            providing services to you, and through the analysis of these data, we will provide you with
                            more upgraded and intimate services. For example, to help us understand the problems users
                            encounter when using the software and services, in order to improve the quality, performance
                            and security of the software and services.</p>
                        <p>2.The Company attaches great importance to the protection of user information.</p>
                        <p>3.The user agrees that the Company has the right to use the user's information resources
                            under the following (including but not limited to) several circumstances:</p>
                        <p>3.1.Performing software verification or upgrade services;</p>
                        <p>3.2.Improve the user's security of use and provide customer support;</p>
                        <p>3.3. to improve or enhance the technology and services of the software, for example, to help
                            us understand the problems users encounter when using the software and services and to help
                            us improve the quality, performance and security of the software and services</p>
                        <p>3.4. sending notices and information about the services provided by the software to
                            users;</p>
                        <p>3.5. the Company may cooperate with third parties to provide users with the relevant network
                            services necessary, and the third party agrees to assume the same responsibility as the
                            Company to protect the privacy of users</p>
                        <p>3.6. the Company may analyze the user database and generate statistical data without
                            disclosing the privacy information of individual users</p>
                        <p>3.7. other acts of use that are conducive to the interests of the user and the achievement of
                            the functions of the Software.</p>
                        <p>4.The Company will take reasonable measures to protect user information and not to disclose
                            or reveal user information to third parties, except for the following cases:</p>
                        <p>4.1, based on the law or the requirements of the government department that the law gives
                            authority to;</p>
                        <p>4.2. In case of emergency, in order to protect the rights and interests of users and the
                            public;</p>
                        <p>4.3. in order to maintain the Company's trademark rights, patent rights and any other
                            legitimate rights and interests;</p>
                        <p>4.4. Where the user agrees to disclose the user's information.</p>
                        <p>5.The use of third-party services or equipment by the user may result in the disclosure of
                            user information to third parties by other means, the user needs to understand the relevant
                            terms of third-party protection of user information, and the Company does not assume the
                            risks arising therefrom.</p>
                        <p>6.The user can choose not to provide the Company with user information or cancel the
                            collection of certain information by the software according to the product settings. The
                            Company shall not be responsible for the failure of the relevant service functions due to
                            the user's failure to provide information to the Company.</p><br/>
                        <p>V. Legal Liability and Exclusion of Liability</p>
                        <p>1. The user acknowledges that the Company's software will make every reasonable effort to
                            protect the privacy and integrity of the user's computer resources and computer
                            communications, but the user acknowledges and agrees that the Company cannot provide any
                            promises or guarantees in this regard.</p>
                        <p>2. The Company especially draws the user's attention to the fact that in order to protect the
                            autonomy of the software development and adjustment, the Company has the right to modify or
                            discontinue the software license at any time at its own discretion without notice to the
                            user, and if necessary, the modification or discontinuation will be published in the form of
                            a notice on the important pages of the system.</p>
                        <p>3. The user agrees to indemnify and hold harmless the Company and its partner companies and
                            affiliates from any claims, demands or losses, including reasonable attorney's fees,
                            asserted by any third party as a result of or arising out of the user's violation of the
                            provisions of this Statement or the related Terms of Service. In this regard, the Company
                            has the right to take measures including, but not limited to, interrupting the use of the
                            license, stopping the provision of services, restricting the use, and legal prosecution,
                            depending on the nature of the user's behavior.</p>
                        <p>4. The software will be tested in detail, but there is no guarantee that it will be fully
                            compatible with all hardware and software systems, or that the software will be completely
                            error-free. In case of incompatibility and errors, users can report the situation to our
                            company for technical support. If the compatibility problem cannot be solved, the user can
                            delete the software.</p>
                        <p>5.To the maximum extent permitted by applicable law, the Company shall not be liable for
                            damages and risks arising from the use or inability to use the Software, including but not
                            limited to direct or indirect personal damages, loss of business winnings, interruption of
                            trade, loss of business information or any other economic loss.</p>
                        <p>6.The Company shall not be liable for any interruption of network services, loss of data or
                            other defects caused by reasons beyond its control (including system upgrade and
                            maintenance, telecommunications system or Internet network failure, computer failure,
                            computer system problems) or any other force majeure reasons.</p>
                        <p>7. If the user violates this statement and causes damage to the Company, the Company has the
                            right to take measures including, but not limited to, interrupting the use of the license,
                            stopping the provision of services, restricting the use, and pursuing the law.</p><br/>
                        <p>VI. Other Terms</p>
                        <p>1.The invalidity of any part or all of the terms of this Agreement shall not affect the
                            validity of other terms.</p>
                        <p>2.The interpretation, validity and dispute resolution of this Agreement shall be governed by
                            the laws of the People's Republic of China. If any dispute or controversy arises between the
                            user and the Company, it shall first be resolved through friendly consultation, and if
                            consultation fails, the user hereby fully agrees to submit the dispute or controversy to the
                            jurisdiction of the People's Court of the Company's location.</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="agreeTip">
                        <input type="checkbox" class="tip-check" id="tip-check">
                        <p class="tip-text">本人确认已阅读及理解上述政策，并同意遵守</p> <br/>
                        <div class="errorMsg" id="errorMsg" style="display: none">请阅读并勾选同意上述政策</div>
                    </div>
                    <div class="button-group">
                        <button type="button" class="btn btn-cancle" data-dismiss="modal" id="cancle">取消</button>
                        <button type="button" class="btn btn-confirm" id="confirm">确定</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
<#--背景-->
<#--<div class="bg1"></div>-->
<#--内容-->
<#--<div class="bg">-->
<#--<div class="head">-->
<#--<div id="logo" class="logo">-->
<#--</div>-->
<#--<div class="logo-content">-->
<#--<a class="head-font" href="https://help.fmone.cn/" target="_blank">产品使用</a>-->
<#--<a class="head-font" href="http://www.facilityone.cn/" target="_blank">公司官网</a>-->
<#--</div>-->
<#--</div>-->
<#--<div class="body">-->
<#--<div class="body-con">-->
<#--<div class="con-margin">-->
<#--<div class="login-title"><span>登录</span><span>F-ONE系统</span></div>-->
<#--<div class="hr-title"></div>-->
<#--<div class="account_text">账号：</div>-->
<#--<div class="account_input">-->
<#--<input type="text" class="username form-control" name="username" id="username"/>-->
<#--</div>-->
<#--<div class="password_text">密码：</div>-->
<#--<div class="password_input">-->
<#--<input type="password" id="password" name="password" class="password form-control"/>-->
<#--<input type="hidden" name="client_id" value="${client_id}">-->
<#--<input type="hidden" name="response_type" value="${response_type}">-->
<#--<input type="hidden" name="redirect_uri" value="${redirect_uri}"></div>-->
<#--<input type="hidden" name="ucperson_language" id="ucperson_language" value="zh_CN">-->

<#--<div class="login_btn" id="login_btn">登录</div>-->
<#--<div class="login_language">-->
<#--<div class="l_line"></div>-->
<#--<div class="l_text">语言</div>-->
<#--<div class="l_line"></div>-->
<#--</div>-->
<#--<div class="login_choose_lan">-->
<#--<div id="login_choose_zh" class="login_choose_zh login_choose_active login_choose_l">汉</div>-->
<#--<div id="login_choose_en" class="login_choose_en login_choose_inactive login_choose_l">EN</div>-->
<#--</div>-->
<#--</div>-->
<#--</div>-->
<#--</div>-->
<#--<div class="foot">-->
<#--<div class="foot-size foot-size-sp">Powered by FacilityONE</div>-->
<#--<div class="foot-content foot-size">费哲软件提供技术支持</div>-->
<#--</div>-->
<#--</div>-->
<!--/PAGE -->
<!-- JAVASCRIPTS -->
<!-- Placed at the end of the document so the pages load faster -->
<!-- JQUERY -->
<script src="${baseStaticPath}/resource/js/js-plugin/jquery/jquery-2.0.3.min.js"></script>
<script src="${baseStaticPath}/resource/js/js-plugin/jquery-ui-1.10.3.custom/js/jquery-ui-1.10.3.custom.min.js"></script>
<script src="${baseStaticPath}/resource/js/js-plugin/bootstrap/js/bootstrap.js"></script>

<!-- CanvasBG Plugin(creates mousehover effect) -->
<script src="${baseStaticPath}/resource/js/login/canvasbg.js"></script>
<script src="${baseStaticPath}/resource/js/js-plugin/jQuery-Cookie/jquery.cookie.min.js"></script>
<#assign _STATIC_COMMON_="${baseStaticPath}/resource">
<script src="${_STATIC_COMMON_}/js/logo.js"></script>

<script type="text/javascript" charset="utf-8">
    jQuery(document).ready(function () {
        $.ajax({
            url:PUBLIC_PATH+'/logo/CustomSettings',
            type: 'get',
            success: function (data) {
                var  data=data.data;
                if(data && data.loginPageLogo){
                    $("#logo").css("background","url("+PUBLIC_PATH+data.loginPageLogo+"/img) 30%");
                }else {
                    $("#logo").css("background","url("+PUBLIC_PATH+"/resource/img/logo2.png) 30%");
                }

            }
        });

        /*"use strict";

        // Init CanvasBG and pass target starting location
        CanvasBG.init({
            Loc: {
                x: window.innerWidth / 2,
                y: window.innerHeight / 3.3
            },
        });*/
        $('#login_btn').on('click', function () {
            // $("#loginModal").modal();
            loginCheck();
        });

        $('#loginLanguage').on('click', function (e) {
            $('#nav-dropdown').slideToggle('fast');
            e.stopPropagation();
        });
        $('html').click(function() {
            $('.nav-dropdown').hide();
        });

        $('#login_choose_zh').on('click', function () {
            // $('.login-title span:nth-child(1)').html('登录');
            // $('.login-title span:nth-child(2)').html('F-ONE系统');
            // $('.account_text').html('账号：');
            // $('.password_text').html('密码：');
            $('#login_btn_span').html('登录');
            // $('.l_text').html('语言');
            // $('.l_line').css("width", "121px");
            $('#ucperson_language').val('zh_CN');
            $('#login_language').html('简体中文');
            $('#login_choose_zh').html('简体中文');
            $('#login_choose_en').html('English');
            $("#username").attr("placeholder","账号");
            $("#password").attr("placeholder","密码");
            $("#staticBackdropLabel").html("服务协议及及隐私政策");
            $(".tip-text").html("本人确认已阅读及理解上述政策，并同意遵守");
            $("#errorMsg").html("请阅读并勾选同意上述政策");
            $("#cancle").html("取消");
            $("#confirm").html("确定");
            // $('#login_choose_zh').removeClass('login_choose_inactive').addClass('login_choose_active');
            // $('#login_choose_en').removeClass('login_choose_active').addClass('login_choose_inactive');
        });

        $('#login_choose_HK').on('click', function () {
            // $('.login-title span:nth-child(1)').html('登录');
            // $('.login-title span:nth-child(2)').html('F-ONE系统');
            // $('.account_text').html('账号：');
            // $('.password_text').html('密码：');
            $('#login_btn_span').html('登入');
            // $('.l_text').html('语言');
            // $('.l_line').css("width", "121px");
            $('#ucperson_language').val('zh_HK');
            $('#login_language').html('簡體中文');
            $('#login_choose_zh').html('简体中文');
            $('#login_choose_en').html('English');
            $("#username").attr("placeholder","帳號");
            $("#password").attr("placeholder","密碼");
            $("#staticBackdropLabel").html("服務協定及及隱私政策");
            $(".tip-text").html("本人確認已閱讀及理解上述政策，並同意遵守");
            $("#errorMsg").html("請閱讀並勾選同意上述政策");
            $("#cancle").html("取消");
            $("#confirm").html("確定");
            // $('#login_choose_zh').removeClass('login_choose_inactive').addClass('login_choose_active');
            // $('#login_choose_en').removeClass('login_choose_active').addClass('login_choose_inactive');
        });
        $('#login_choose_en').on('click', function () {
            // $('.login-title span:nth-child(1)').html('');
            // $('.login-title span:nth-child(2)').html('F-ONE System');
            // $('.account_text').html('Account:');
            // $('.password_text').html('Password:');
            $('#login_btn_span').html('Login');
            // $('.l_text').html('Language');
            // $('.l_line').css("width", "95px");
            $('#ucperson_language').val('en_US');
            $('#login_language').html('English');
            $('#login_choose_zh').html('简体中文');
            $('#login_choose_en').html('English');
            $("#username").attr("placeholder","Account");
            $("#password").attr("placeholder","Password");
            $("#staticBackdropLabel").html("Service Agreement and Privacy Policy");
            $(".tip-text").html("I confirm that I have read and understood the above policy and agree to comply with it");
            $("#errorMsg").html("Please read and check to agree to the above policy");
            $("#cancle").html("Cancle");
            $("#confirm").html("Confirm");
            // $('#login_choose_en').removeClass('login_choose_inactive').addClass('login_choose_active');
            // $('#login_choose_zh').removeClass('login_choose_active').addClass('login_choose_inactive');
        });
        //监听滚动到底部
        $("#modal-body").scroll(function() {
            var scrollTop = $("#modal-body")[0].scrollTop;
            var clientHeight = $('#modal-body').height();
            var scrollHeight = $('#agreementswipper')[0].scrollHeight;
            if (scrollHeight - clientHeight <= scrollTop + 10) {
                $("#confirm").attr("disabled",false);;
                // $("#confirm").css({backgroundColor: "#2097d0", opacity: 1})
            }
        });
        $('#loginModal').on('shown.bs.modal', function () { // 执行一些动作...
            var lang = $("#ucperson_language").val();
            if ('zh_CN' == lang) {
                $('#contentEn').hide();
                $('#contentZh').show();
            } else {
                $('#contentZh').hide();
                $('#contentEn').show();
            }
        })
        //重置modal
        /*$('#loginModal').on('hidden.bs.modal', function() {
            location.reload(true);
        })*/
        $('#loginModal').on('hide.bs.modal', function() {
            $("#modal-body").scrollTop(0,0);
            $("#confirm").attr("disabled",true);
            $('#errorMsg').hide();
        })
        $('#tip-check').on('click', function() {
            if ($('#tip-check').prop('checked')) {
                $('#errorMsg').hide();
            }
        });
        //确认按钮的点击
        $('#confirm').on('click', function() {
            if (!$('#tip-check').prop('checked')) {
                return $('#errorMsg').show();
            };
            login();
            /*var lang = $("#ucperson_language").val();
            if (null == lang) {
                lang = 'zh_CN';
            }
            var checkuser = $('#loginModal').attr('checkuser');
            $.ajax({
                url: PUBLIC_PATH + '/clause',
                type: "get",
                contentType: "application/json",
                success: function (response) {
                    if (response.status == "success") {
                        window.location = PUBLIC_PATH + "/main_v2/index?current_locale=" + lang+"#__aurl=%2Fchart%2Fdashboard%2Findex_new";
                    }else{
                        response.message && alert(response.message);
                    }
                },
                "error": function (XMLHttpRequest, textStatus, errorThrown) {
                    var Result = $.parseJSON(XMLHttpRequest.responseText);
                    if (Result.code >= 500 && Result.code <= 599 && Result.status == "fail") {//fail
                        Result.message && alert(Result.message);
                    }
                }
            });*/
        });
    });

    $(function () {
        document.onkeydown = function (e) {
            var ev = document.all ? window.event : e;
            if (ev.keyCode == 13) {
                loginCheck();
            }
        }
    });
    function loginCheck() {
        var username = $("input[name='username']").val();
        var password = $("input[name='password']").val();

        if (password.length < 2 || password.length > 64) {
            alert('密码长度2-64');
            return;
        }

        var org_url = window.location.href.toString();
        localStorage.removeItem("from");
        if (org_url.indexOf("?from=") != -1) {
            localStorage.from = org_url.substring(org_url.indexOf("?from=") + 6);
        }

        var client_id = $("input[name='client_id']").val();
        var response_type = $("input[name='response_type']").val();
        var lang = $("#ucperson_language").val();
        if (null == lang) {
            lang = 'zh_CN';
        }
        var redirect_uri = $("input[name='redirect_uri']").val();

        var user = {"loginCode": username, "loginPwd": password, "source": "web", "captcha": "123456", "locale":lang};
        $.ajax({
            url: PUBLIC_PATH+'/check/clause',
            type: "POST",
            contentType: "application/json",
            data: JSON.stringify(user),
            success: function (response) {
                if (response.data) {
                    login();
                }else{
                    $("#loginModal").modal();
                }
            },
            "error": function (XMLHttpRequest, textStatus, errorThrown) {
                var Result = $.parseJSON(XMLHttpRequest.responseText);
                if (Result.code >= 500 && Result.code <= 599 && Result.status == "fail") {//fail
                    Result.message && alert(Result.message);
                }
            }
        });
    }
    function login() {
        var username = $("input[name='username']").val();
        var password = $("input[name='password']").val();

        var org_url = window.location.href.toString();
        localStorage.removeItem("from");
        if (org_url.indexOf("?from=") != -1) {
            localStorage.from = org_url.substring(org_url.indexOf("?from=") + 6);
        }

        var client_id = $("input[name='client_id']").val();
        var response_type = $("input[name='response_type']").val();
        var lang = $("#ucperson_language").val();
        if (null == lang) {
            lang = 'zh_CN';
        }
        var redirect_uri = $("input[name='redirect_uri']").val();

        var user = {"loginCode": username, "loginPwd": password, "source": "web", "captcha": "123456", "locale":lang};
        $.ajax({
            url: PUBLIC_PATH+'/login',
            type: "POST",
            contentType: "application/json",
            data: JSON.stringify(user),
            success: function (response) {
                if (response.status == "success") {
                    window.location = PUBLIC_PATH + "/main_v2/index?current_locale=" + lang+"#__aurl=%2Fchart%2Fdashboard%2Findex_new";
                }else{
                    response.message && alert(response.message);
                }

                localStorage.removeItem("i18nInfo")
            },
            "error": function (XMLHttpRequest, textStatus, errorThrown) {
                var Result = $.parseJSON(XMLHttpRequest.responseText);
                if (Result.code >= 500 && Result.code <= 599 && Result.status == "fail") {//fail
                    Result.message && alert(Result.message);
                }
            }
        });
    }
</script>
<script>document.documentElement.lang = "${i18nLocale}";</script>
<script type="text/javascript" charset="utf-8">
    jQuery(document).ready(function () {
        $("#ucperson_language").change(function () {
            var date = new Date();
            date.setTime(date.getTime() + (30 * 24 * 60 * 60 * 1000)); //7天后的这个时候过期
            $.cookie('lang', $(this).val(), {path: '/', expires: date});
            window.location.href = getRefreshUri($(this).val());
        });

        var query_object = getUriParams();
        if (!query_object["i18n"] && $.cookie('lang')) {
            window.location.href = getRefreshUri($.cookie('lang'));
        }
        //$("#ucperson_language").val(query_object["i18n"]);
    });
    var getRefreshUri = function (i18nv) {
        var query_object = getUriParams();
        query_object["i18n"] = i18nv;
        var query_array = [];
        for (var u in query_object) {
            query_array.push(u + "=" + query_object[u]);
        }
        return window.location.protocol + "//" + window.location.host + "/login?" + query_array.join("&") + window.location.hash;
    }
    var getUriParams = function () {
        var query_object = {};
        var query = window.location.search.substring(1);
        if (query) {
            var vars = query.split("&");
            for (var i = 0; i < vars.length; i++) {
                var pair = vars[i].split("=");
                if (pair.length == 2) {
                    query_object[pair[0]] = pair[1];
                } else if (pair.length == 3) {
                    query_object[pair[0]] = pair[1] + "=" + pair[2];
                }
            }
        }
        return query_object;
    }
</script>
<!-- /JAVASCRIPTS -->
</body>
</html>
