/**
 * Created by bill on 2018/7/12.
 * 内嵌webview 进行专属的ajax处理
 */

$(function () {
    jQuery.ajaxPrefilter(function (options) {
        if (!/^http/.test(options.url)) {
            options.url = PUBLIC_PATH + options.url;
        }
    });

    $.ajaxSetup({
        contentType: "application/json",
        dataType: "json",
        processData: false,
        cache: false,//关闭浏览器get请求的缓存（IE）
        beforeSend: function (xhr) {
            var finalCurrentProject = null;
            var currentproject = getCurrentProject();
            var currentprojectFromUrl = FO.get_uri_param("current_project");
            if(!currentproject&&currentprojectFromUrl){
                finalCurrentProject = currentprojectFromUrl;
            }else if(!currentprojectFromUrl&&currentproject){
                finalCurrentProject = currentproject;
            }else{
                if(currentproject!=currentprojectFromUrl){
                    finalCurrentProject = currentprojectFromUrl;
                }else{
                    finalCurrentProject = currentproject;
                }
            }
            var tempProject = finalCurrentProject;
            var currentproject = tempProject?tempProject:0;
            xhr.setRequestHeader('CurrentProject', currentproject);

            /* 移动端webview */
            if(FO.get_uri_param("device_id")) {
                xhr.setRequestHeader("device_id", FO.get_uri_param("device_id"));
            }

            if(FO.get_uri_param("device-type")) {
                xhr.setRequestHeader("device-type", FO.get_uri_param("device-type"));
            }
        }
    });
});



