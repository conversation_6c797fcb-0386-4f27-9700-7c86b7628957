<#assign  _STATIC_RE_="${baseStaticPath}/resource">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<meta content="text/html; charset=UTF-8" http-equiv="content-type">
<meta charset="utf-8">
<title>${title!}</title>
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1, user-scalable=no" name="viewport">
<meta content="" name="description">
<meta content="FacilityOne" name="author">
<link rel="shortcut icon" href="${baseStaticPath}/favicon.ico"/>
<link rel="bookmark" href="${baseStaticPath}/favicon.ico"/>

<!-- 引入font样式 -->
<link rel="stylesheet" href="${_STATIC_RE_}/css/font-awesome/css/font-awesome.min.css">

<!-- 引入element样式 -->
<link rel="stylesheet" href="${_STATIC_RE_}/app/components/element.ui/theme-default/index.css">
<link rel="stylesheet" href="${_STATIC_RE_}/app/components/element.ui/theme-default/reset.css">

<!-- 引入页面样式 -->
<link rel="stylesheet" href="${_STATIC_RE_}/app/components/shang.extensions/smooth-scrollbar.css">
<#-- 改变标题和小图标 -->
<link rel="stylesheet" href="${_STATIC_RE_}/css/fmone-v2.1/index.css">
<script src="${_STATIC_RE_}/js/logo.js"></script>
<style>
    [v-cloak] {display: none;}
</style>
<script type="text/javascript">
    var getCurrentProject = function () {
        var finalCurrentProject = null;
        var currentproject = localStorage.current_project;
        var currentprojectFromUrl = FO.get_uri_param("current_project");
        if(!currentproject&&currentprojectFromUrl){
            finalCurrentProject = currentprojectFromUrl;
        }else if(!currentprojectFromUrl&&currentproject){
            currentprojectFromUrl = 0;
            finalCurrentProject = currentprojectFromUrl;
        }else{
            if(currentproject!=currentprojectFromUrl){
                finalCurrentProject = currentprojectFromUrl;
            }else{
                if (currentprojectFromUrl == undefined) {
                    currentprojectFromUrl = 0;
                }
                if (currentproject == undefined) {
                    currentproject = 0;
                }
                finalCurrentProject = currentproject;
            }
        }
        return finalCurrentProject;
    };
    var PUBLIC_PATH = '${baseStaticPath}';
</script>

<script type="text/javascript">
    window.iframeAutoHeight = function() {
        setTimeout(function () {
            try {
                var iframe= window.top.window.document.querySelectorAll("iframe")[0];
                if(navigator.userAgent.indexOf("MSIE")>0||navigator.userAgent.indexOf("rv:11")>0||navigator.userAgent.indexOf("Firefox")>0){
                    iframe.height=iframe.contentWindow.document.body.scrollHeight;
                }else{
                    iframe.height=iframe.contentWindow.document.documentElement.scrollHeight;
                }
            } catch (e) {

            }
        },100)
    }
</script>
