module.exports =
/******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};

/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {

/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId])
/******/ 			return installedModules[moduleId].exports;

/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			exports: {},
/******/ 			id: moduleId,
/******/ 			loaded: false
/******/ 		};

/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);

/******/ 		// Flag the module as loaded
/******/ 		module.loaded = true;

/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}


/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;

/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;

/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "/dist/";

/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(0);
/******/ })
/************************************************************************/
/******/ ({

/***/ 0:
/***/ function(module, exports, __webpack_require__) {

	module.exports = __webpack_require__(240);


/***/ },

/***/ 240:
/***/ function(module, exports, __webpack_require__) {

	'use strict';

	var TabPane = __webpack_require__(241);

	/* istanbul ignore next */
	TabPane.install = function (Vue) {
	  Vue.component(TabPane.name, TabPane);
	};

	module.exports = TabPane;

/***/ },

/***/ 241:
/***/ function(module, exports, __webpack_require__) {

	var __vue_exports__, __vue_options__

	/* script */
	__vue_exports__ = __webpack_require__(242)

	/* template */
	var __vue_template__ = __webpack_require__(243)
	__vue_options__ = __vue_exports__ = __vue_exports__ || {}
	if (
	  typeof __vue_exports__.default === "object" ||
	  typeof __vue_exports__.default === "function"
	) {
	__vue_options__ = __vue_exports__ = __vue_exports__.default
	}
	if (typeof __vue_options__ === "function") {
	  __vue_options__ = __vue_options__.options
	}

	__vue_options__.render = __vue_template__.render
	__vue_options__.staticRenderFns = __vue_template__.staticRenderFns

	module.exports = __vue_exports__


/***/ },

/***/ 242:
/***/ function(module, exports) {

	'use strict';

	module.exports = {
	  name: 'el-tab-pane',

	  props: {
	    label: {
	      type: String,
	      required: true
	    },
	    name: String
	  },

	  data: function data() {
	    return {
	      counter: 0,
	      transition: '',
	      paneStyle: {
	        position: 'relative'
	      },
	      key: ''
	    };
	  },
	  created: function created() {
	    if (!this.key) {
	      this.key = this.$parent.$children.indexOf(this) + 1 + '';
	    }
	  },


	  computed: {
	    show: function show() {
	      return this.$parent.currentName === this.key;
	    }
	  },

	  destroyed: function destroyed() {
	    this.$el.remove();
	  },


	  watch: {
	    name: {
	      immediate: true,
	      handler: function handler(val) {
	        this.key = val;
	      }
	    },
	    '$parent.currentName': function $parentCurrentName(newValue, oldValue) {
	      if (this.key === newValue) {
	        this.transition = newValue > oldValue ? 'slideInRight' : 'slideInLeft';
	      }
	      if (this.key === oldValue) {
	        this.transition = oldValue > newValue ? 'slideInRight' : 'slideInLeft';
	      }
	    }
	  }
	};

/***/ },

/***/ 243:
/***/ function(module, exports) {

	module.exports={render:function (){with(this) {
	  return _h('div', {
	    directives: [{
	      name: "show",
	      rawName: "v-show",
	      value: (show && $slots.default),
	      expression: "show && $slots.default"
	    }],
	    staticClass: "el-tab-pane"
	  }, [_t("default")])
	}},staticRenderFns: []}

/***/ }

/******/ });