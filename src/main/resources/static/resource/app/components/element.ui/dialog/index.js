module.exports =
/******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};

/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {

/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId])
/******/ 			return installedModules[moduleId].exports;

/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			exports: {},
/******/ 			id: moduleId,
/******/ 			loaded: false
/******/ 		};

/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);

/******/ 		// Flag the module as loaded
/******/ 		module.loaded = true;

/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}


/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;

/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;

/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "/dist/";

/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(0);
/******/ })
/************************************************************************/
/******/ ({

/***/ 0:
/***/ function(module, exports, __webpack_require__) {

	module.exports = __webpack_require__(80);


/***/ },

/***/ 80:
/***/ function(module, exports, __webpack_require__) {

	'use strict';

	var ElDialog = __webpack_require__(81);

	/* istanbul ignore next */
	ElDialog.install = function (Vue) {
	  Vue.component(ElDialog.name, ElDialog);
	};

	module.exports = ElDialog;

/***/ },

/***/ 81:
/***/ function(module, exports, __webpack_require__) {

	var __vue_exports__, __vue_options__

	/* script */
	__vue_exports__ = __webpack_require__(82)

	/* template */
	var __vue_template__ = __webpack_require__(84)
	__vue_options__ = __vue_exports__ = __vue_exports__ || {}
	if (
	  typeof __vue_exports__.default === "object" ||
	  typeof __vue_exports__.default === "function"
	) {
	__vue_options__ = __vue_exports__ = __vue_exports__.default
	}
	if (typeof __vue_options__ === "function") {
	  __vue_options__ = __vue_options__.options
	}

	__vue_options__.render = __vue_template__.render
	__vue_options__.staticRenderFns = __vue_template__.staticRenderFns

	module.exports = __vue_exports__


/***/ },

/***/ 82:
/***/ function(module, exports, __webpack_require__) {

	'use strict';

	exports.__esModule = true;

	var _vuePopup = __webpack_require__(83);

	var _vuePopup2 = _interopRequireDefault(_vuePopup);

	function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

	exports.default = {
	  name: 'el-dialog',

	  mixins: [_vuePopup2.default],

	  props: {
	    title: {
	      type: String,
	      default: ''
	    },

	    modal: {
	      type: Boolean,
	      default: true
	    },

	    lockScroll: {
	      type: Boolean,
	      default: true
	    },

	    closeOnClickModal: {
	      type: Boolean,
	      default: true
	    },

	    closeOnPressEscape: {
	      type: Boolean,
	      default: true
	    },

	    size: {
	      type: String,
	      default: 'small'
	    },

	    customClass: {
	      type: String,
	      default: ''
	    },

	    top: {
	      type: String,
	      default: '15%'
	    }
	  },

	  watch: {
	    value: function value(val) {
	      var _this = this;

	      if (val) {
	        this.$emit('open');
	        this.$nextTick(function () {
	          _this.$refs.dialog.scrollTop = 0;
	        });
	      } else {
	        this.$emit('close');
	      }
	    }
	  },

	  computed: {
	    sizeClass: function sizeClass() {
	      return 'el-dialog--' + this.size;
	    },
	    style: function style() {
	      return this.size === 'full' ? {} : { 'margin-bottom': '50px', 'top': this.top };
	    }
	  },

	  methods: {
	    handleWrapperClick: function handleWrapperClick() {
	      if (this.closeOnClickModal) {
	        this.$emit('input', false);
	      }
	    }
	  },

	  mounted: function mounted() {
	    if (this.value) {
	      this.rendered = true;
	      this.open();
	    }
	  }
	}; //
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//

/***/ },

/***/ 83:
/***/ function(module, exports) {

	module.exports = require("vue-popup");

/***/ },

/***/ 84:
/***/ function(module, exports) {

	module.exports={render:function (){with(this) {
	  return _h('transition', {
	    attrs: {
	      "name": "dialog-fade"
	    }
	  }, [_h('div', {
	    directives: [{
	      name: "show",
	      rawName: "v-show",
	      value: (value),
	      expression: "value"
	    }],
	    staticClass: "el-dialog__wrapper",
	    on: {
	      "click": function($event) {
	        if ($event.target !== $event.currentTarget) return;
	        handleWrapperClick($event)
	      }
	    }
	  }, [_h('div', {
	    ref: "dialog",
	    staticClass: "el-dialog",
	    class: [sizeClass, customClass],
	    style: (style)
	  }, [_h('div', {
	    staticClass: "el-dialog__header"
	  }, [_h('span', {
	    staticClass: "el-dialog__title"
	  }, [_s(title)]), _h('div', {
	    staticClass: "el-dialog__headerbtn"
	  }, [_h('i', {
	    staticClass: "el-dialog__close el-icon el-icon-close",
	    on: {
	      "click": function($event) {
	        close()
	      }
	    }
	  })])]), (rendered) ? _h('div', {
	    staticClass: "el-dialog__body"
	  }, [_t("default")]) : _e(), ($slots.footer) ? _h('div', {
	    staticClass: "el-dialog__footer"
	  }, [_t("footer")]) : _e()])])])
	}},staticRenderFns: []}

/***/ }

/******/ });