module.exports =
/******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};

/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {

/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId])
/******/ 			return installedModules[moduleId].exports;

/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			exports: {},
/******/ 			id: moduleId,
/******/ 			loaded: false
/******/ 		};

/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);

/******/ 		// Flag the module as loaded
/******/ 		module.loaded = true;

/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}


/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;

/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;

/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "/dist/";

/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(0);
/******/ })
/************************************************************************/
/******/ ({

/***/ 0:
/***/ function(module, exports, __webpack_require__) {

	module.exports = __webpack_require__(273);


/***/ },

/***/ 9:
/***/ function(module, exports) {

	module.exports = require("element-ui/lib/utils/clickoutside");

/***/ },

/***/ 38:
/***/ function(module, exports) {

	module.exports = require("element-ui/lib/mixins/emitter");

/***/ },

/***/ 50:
/***/ function(module, exports, __webpack_require__) {

	var __vue_exports__, __vue_options__

	/* script */
	__vue_exports__ = __webpack_require__(51)

	/* template */
	var __vue_template__ = __webpack_require__(56)
	__vue_options__ = __vue_exports__ = __vue_exports__ || {}
	if (
	  typeof __vue_exports__.default === "object" ||
	  typeof __vue_exports__.default === "function"
	) {
	__vue_options__ = __vue_exports__ = __vue_exports__.default
	}
	if (typeof __vue_options__ === "function") {
	  __vue_options__ = __vue_options__.options
	}

	__vue_options__.render = __vue_template__.render
	__vue_options__.staticRenderFns = __vue_template__.staticRenderFns

	module.exports = __vue_exports__


/***/ },

/***/ 51:
/***/ function(module, exports, __webpack_require__) {

	'use strict';

	exports.__esModule = true;

	var _vue = __webpack_require__(52);

	var _vue2 = _interopRequireDefault(_vue);

	var _clickoutside = __webpack_require__(9);

	var _clickoutside2 = _interopRequireDefault(_clickoutside);

	var _util = __webpack_require__(53);

	var _vuePopper = __webpack_require__(55);

	var _vuePopper2 = _interopRequireDefault(_vuePopper);

	var _emitter = __webpack_require__(38);

	var _emitter2 = _interopRequireDefault(_emitter);

	function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

	var NewPopper = {
	  props: {
	    appendToBody: _vuePopper2.default.props.appendToBody,
	    offset: _vuePopper2.default.props.offset,
	    boundariesPadding: _vuePopper2.default.props.boundariesPadding
	  },
	  methods: _vuePopper2.default.methods,
	  data: _vuePopper2.default.data,
	  beforeDestroy: _vuePopper2.default.beforeDestroy
	}; //
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//

	var RANGE_SEPARATOR = ' - ';
	var DEFAULT_FORMATS = {
	  date: 'yyyy-MM-dd',
	  month: 'yyyy-MM',
	  datetime: 'yyyy-MM-dd HH:mm:ss',
	  time: 'HH:mm:ss',
	  timerange: 'HH:mm:ss',
	  daterange: 'yyyy-MM-dd',
	  datetimerange: 'yyyy-MM-dd HH:mm:ss'
	};
	var HAVE_TRIGGER_TYPES = ['date', 'datetime', 'time', 'time-select', 'week', 'month', 'year', 'daterange', 'timerange', 'datetimerange'];
	var DATE_FORMATTER = function DATE_FORMATTER(value, format) {
	  return (0, _util.formatDate)(value, format);
	};
	var DATE_PARSER = function DATE_PARSER(text, format) {
	  text = text.split(':');
	  if (text.length > 1) text = text.map(function (item) {
	    return item.slice(-2);
	  });
	  text = text.join(':');

	  return (0, _util.parseDate)(text, format);
	};
	var RANGE_FORMATTER = function RANGE_FORMATTER(value, format) {
	  if (Array.isArray(value) && value.length === 2) {
	    var start = value[0];
	    var end = value[1];

	    if (start && end) {
	      return (0, _util.formatDate)(start, format) + RANGE_SEPARATOR + (0, _util.formatDate)(end, format);
	    }
	  }
	  return '';
	};
	var RANGE_PARSER = function RANGE_PARSER(text, format) {
	  var array = text.split(RANGE_SEPARATOR);
	  if (array.length === 2) {
	    var range1 = array[0].split(':').map(function (item) {
	      return item.slice(-2);
	    }).join(':');
	    var range2 = array[1].split(':').map(function (item) {
	      return item.slice(-2);
	    }).join(':');
	    return [(0, _util.parseDate)(range1, format), (0, _util.parseDate)(range2, format)];
	  }
	  return [];
	};
	var TYPE_VALUE_RESOLVER_MAP = {
	  default: {
	    formatter: function formatter(value) {
	      if (!value) return '';
	      return '' + value;
	    },
	    parser: function parser(text) {
	      if (text === undefined || text === '') return null;
	      return text;
	    }
	  },
	  week: {
	    formatter: function formatter(value) {
	      if (value instanceof Date) {
	        var weekNumber = (0, _util.getWeekNumber)(value);
	        return value.getFullYear() + 'w' + (weekNumber > 9 ? weekNumber : '0' + weekNumber);
	      }
	      return value;
	    },
	    parser: function parser(text) {
	      var array = (text || '').split('w');
	      if (array.length === 2) {
	        var year = Number(array[0]);
	        var month = Number(array[1]);

	        if (!isNaN(year) && !isNaN(month) && month < 54) {
	          return text;
	        }
	      }
	      return null;
	    }
	  },
	  date: {
	    formatter: DATE_FORMATTER,
	    parser: DATE_PARSER
	  },
	  datetime: {
	    formatter: DATE_FORMATTER,
	    parser: DATE_PARSER
	  },
	  daterange: {
	    formatter: RANGE_FORMATTER,
	    parser: RANGE_PARSER
	  },
	  datetimerange: {
	    formatter: RANGE_FORMATTER,
	    parser: RANGE_PARSER
	  },
	  timerange: {
	    formatter: RANGE_FORMATTER,
	    parser: RANGE_PARSER
	  },
	  time: {
	    formatter: DATE_FORMATTER,
	    parser: DATE_PARSER
	  },
	  month: {
	    formatter: DATE_FORMATTER,
	    parser: DATE_PARSER
	  },
	  year: {
	    formatter: function formatter(value) {
	      if (!value) return '';
	      return '' + value;
	    },
	    parser: function parser(text) {
	      var year = Number(text);
	      if (!isNaN(year)) return year;

	      return null;
	    }
	  },
	  number: {
	    formatter: function formatter(value) {
	      if (!value) return '';
	      return '' + value;
	    },
	    parser: function parser(text) {
	      var result = Number(text);

	      if (!isNaN(text)) {
	        return result;
	      } else {
	        return null;
	      }
	    }
	  }
	};
	var PLACEMENT_MAP = {
	  left: 'bottom-start',
	  center: 'bottom-center',
	  right: 'bottom-end'
	};

	exports.default = {
	  mixins: [_emitter2.default, NewPopper],

	  props: {
	    format: String,
	    readonly: Boolean,
	    placeholder: String,
	    align: {
	      type: String,
	      default: 'left'
	    },
	    value: {},
	    haveTrigger: {},
	    pickerOptions: {}
	  },

	  directives: { Clickoutside: _clickoutside2.default },

	  data: function data() {
	    return {
	      pickerVisible: false
	    };
	  },


	  watch: {
	    pickerVisible: function pickerVisible(val) {
	      val ? this.showPicker() : this.hidePicker();
	    },
	    value: function value(val) {
	      this.dispatch('form-item', 'el.form.change');
	    }
	  },

	  computed: {
	    triggerClass: function triggerClass() {
	      return this.type.indexOf('time') !== -1 ? 'el-icon-time' : 'el-icon-date';
	    },
	    editable: function editable() {
	      return this.type.indexOf('range') === -1;
	    },
	    selectionMode: function selectionMode() {
	      if (this.type === 'week') {
	        return 'week';
	      } else if (this.type === 'month') {
	        return 'month';
	      } else if (this.type === 'year') {
	        return 'year';
	      }

	      return 'day';
	    },
	    haveTrigger: function haveTrigger() {
	      if (typeof this.showTrigger !== 'undefined') {
	        return this.showTrigger;
	      }
	      return HAVE_TRIGGER_TYPES.indexOf(this.type) !== -1;
	    },


	    visualValue: {
	      get: function get() {
	        var value = this.value;
	        var formatter = (TYPE_VALUE_RESOLVER_MAP[this.type] || TYPE_VALUE_RESOLVER_MAP['default']).formatter;
	        var format = DEFAULT_FORMATS[this.type];

	        return formatter(value, this.format || format);
	      },
	      set: function set(value) {
	        if (value) {
	          var type = this.type;
	          var parser = (TYPE_VALUE_RESOLVER_MAP[type] || TYPE_VALUE_RESOLVER_MAP['default']).parser;
	          var parsedValue = parser(value, this.format || DEFAULT_FORMATS[type]);

	          if (parsedValue) {
	            this.picker.value = parsedValue;
	          }
	          return;
	        }
	        this.picker.value = value;
	      }
	    }
	  },

	  created: function created() {
	    // vue-popper
	    this.options = {
	      boundariesPadding: 0,
	      gpuAcceleration: false
	    };
	    this.placement = PLACEMENT_MAP[this.align] || PLACEMENT_MAP.left;
	  },


	  methods: {
	    handleClose: function handleClose() {
	      this.pickerVisible = false;
	    },
	    handleFocus: function handleFocus() {
	      var type = this.type;

	      if (HAVE_TRIGGER_TYPES.indexOf(type) !== -1 && !this.pickerVisible) {
	        this.pickerVisible = true;
	      }
	      this.$emit('focus', this);
	    },
	    handleBlur: function handleBlur() {
	      this.$emit('blur', this);
	      this.dispatch('form-item', 'el.form.blur');
	    },
	    handleKeydown: function handleKeydown(event) {
	      var keyCode = event.keyCode;
	      var target = event.target;
	      var selectionStart = target.selectionStart;
	      var selectionEnd = target.selectionEnd;
	      var length = target.value.length;

	      // tab
	      if (keyCode === 9) {
	        this.pickerVisible = false;
	        // enter
	      } else if (keyCode === 13) {
	        this.pickerVisible = this.picker.visible = false;
	        this.visualValue = target.value;
	        target.blur();
	        // left
	      } else if (keyCode === 37) {
	        event.preventDefault();

	        if (selectionEnd === length && selectionStart === length) {
	          target.selectionStart = length - 2;
	        } else if (selectionStart >= 3) {
	          target.selectionStart -= 3;
	        } else {
	          target.selectionStart = 0;
	        }
	        target.selectionEnd = target.selectionStart + 2;
	        // right
	      } else if (keyCode === 39) {
	        event.preventDefault();
	        if (selectionEnd === 0 && selectionStart === 0) {
	          target.selectionEnd = 2;
	        } else if (selectionEnd <= length - 3) {
	          target.selectionEnd += 3;
	        } else {
	          target.selectionEnd = length;
	        }
	        target.selectionStart = target.selectionEnd - 2;
	      }
	    },
	    hidePicker: function hidePicker() {
	      if (this.picker) {
	        this.picker.resetView && this.picker.resetView();
	        this.pickerVisible = this.picker.visible = false;
	        this.destroyPopper();
	      }
	    },
	    showPicker: function showPicker() {
	      var _this = this;

	      if (!this.picker) {
	        this.panel.defaultValue = this.value;
	        this.picker = new _vue2.default(this.panel).$mount(document.createElement('div'));
	        this.popperElm = this.picker.$el;
	        this.picker.width = this.$refs.reference.getBoundingClientRect().width;
	        this.picker.showTime = this.type === 'datetime' || this.type === 'datetimerange';
	        this.picker.selectionMode = this.selectionMode;
	        if (this.format) {
	          this.picker.format = this.format;
	        }

	        var options = this.pickerOptions;

	        if (options && options.selectableRange) {
	          (function () {
	            var ranges = options.selectableRange;
	            var parser = TYPE_VALUE_RESOLVER_MAP.datetimerange.parser;
	            var format = DEFAULT_FORMATS.timerange;

	            ranges = Array.isArray(ranges) ? ranges : [ranges];
	            _this.picker.selectableRange = ranges.map(function (range) {
	              return parser(range, format);
	            });
	          })();
	        }

	        if (this.type === 'time-select' && options) {
	          this.$watch('pickerOptions.minTime', function (val) {
	            _this.picker.minTime = val;
	          });
	        }

	        for (var option in options) {
	          if (options.hasOwnProperty(option) &&
	          // 忽略 time-picker 的该配置项
	          option !== 'selectableRange') {
	            this.picker[option] = options[option];
	          }
	        }

	        this.$el.appendChild(this.picker.$el);
	        this.pickerVisible = this.picker.visible = true;
	        this.picker.resetView && this.picker.resetView();

	        this.picker.$on('dodestroy', this.doDestroy);
	        this.picker.$on('pick', function (date) {
	          var visible = arguments.length <= 1 || arguments[1] === undefined ? false : arguments[1];

	          _this.$emit('input', date);

	          if (!visible) {
	            _this.pickerVisible = _this.picker.visible = !_this.picker.visible;
	          }
	          _this.picker.resetView && _this.picker.resetView();
	        });

	        this.picker.$on('select-range', function (start, end) {
	          setTimeout(function () {
	            _this.$refs.reference.setSelectionRange(start, end);
	            _this.$refs.reference.focus();
	          }, 0);
	        });
	      } else {
	        this.pickerVisible = this.picker.visible = true;
	      }

	      this.updatePopper();

	      if (this.value instanceof Date) {
	        this.picker.date = new Date(this.value.getTime());
	        this.picker.resetView && this.picker.resetView();
	      } else {
	        this.picker.value = this.value;
	      }

	      this.$nextTick(function () {
	        _this.picker.ajustScrollTop && _this.picker.ajustScrollTop();
	      });
	    }
	  }
	};

/***/ },

/***/ 52:
/***/ function(module, exports) {

	module.exports = require("vue");

/***/ },

/***/ 53:
/***/ function(module, exports, __webpack_require__) {

	'use strict';

	exports.__esModule = true;
	exports.limitRange = exports.getRangeHours = exports.nextMonth = exports.prevMonth = exports.getWeekNumber = exports.getStartDateOfMonth = exports.DAY_DURATION = exports.getFirstDayOfMonth = exports.getDayCountOfMonth = exports.parseDate = exports.formatDate = exports.toDate = undefined;

	var _date = __webpack_require__(54);

	var _date2 = _interopRequireDefault(_date);

	function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

	var newArray = function newArray(start, end) {
	  var result = [];
	  for (var i = start; i <= end; i++) {
	    result.push(i);
	  }
	  return result;
	};

	var toDate = exports.toDate = function toDate(date) {
	  date = new Date(date);
	  if (isNaN(date.getTime())) return null;
	  return date;
	};

	var formatDate = exports.formatDate = function formatDate(date, format) {
	  date = toDate(date);
	  if (!date) return '';
	  return _date2.default.format(date, format || 'yyyy-MM-dd');
	};

	var parseDate = exports.parseDate = function parseDate(string, format) {
	  return _date2.default.parse(string, format || 'yyyy-MM-dd');
	};

	var getDayCountOfMonth = exports.getDayCountOfMonth = function getDayCountOfMonth(year, month) {
	  if (month === 3 || month === 5 || month === 8 || month === 10) {
	    return 30;
	  }

	  if (month === 1) {
	    if (year % 4 === 0 && year % 100 !== 0 || year % 400 === 0) {
	      return 29;
	    } else {
	      return 28;
	    }
	  }

	  return 31;
	};

	var getFirstDayOfMonth = exports.getFirstDayOfMonth = function getFirstDayOfMonth(date) {
	  var temp = new Date(date.getTime());
	  temp.setDate(1);
	  return temp.getDay();
	};

	var DAY_DURATION = exports.DAY_DURATION = 86400000;

	var getStartDateOfMonth = exports.getStartDateOfMonth = function getStartDateOfMonth(year, month) {
	  var result = new Date(year, month, 1);
	  var day = result.getDay();

	  if (day === 0) {
	    result.setTime(result.getTime() - DAY_DURATION * 7);
	  } else {
	    result.setTime(result.getTime() - DAY_DURATION * day);
	  }

	  return result;
	};

	var getWeekNumber = exports.getWeekNumber = function getWeekNumber(src) {
	  var date = new Date(src.getTime());
	  date.setHours(0, 0, 0, 0);
	  // Thursday in current week decides the year.
	  date.setDate(date.getDate() + 3 - (date.getDay() + 6) % 7);
	  // January 4 is always in week 1.
	  var week1 = new Date(date.getFullYear(), 0, 4);
	  // Adjust to Thursday in week 1 and count number of weeks from date to week 1.
	  return 1 + Math.round(((date.getTime() - week1.getTime()) / 86400000 - 3 + (week1.getDay() + 6) % 7) / 7);
	};

	var prevMonth = exports.prevMonth = function prevMonth(src) {
	  var year = src.getFullYear();
	  var month = src.getMonth();
	  var date = src.getDate();

	  var newYear = month === 0 ? year - 1 : year;
	  var newMonth = month === 0 ? 11 : month - 1;

	  var newMonthDayCount = getDayCountOfMonth(newYear, newMonth);
	  if (newMonthDayCount < date) {
	    src.setDate(newMonthDayCount);
	  }

	  src.setMonth(newMonth);
	  src.setFullYear(newYear);

	  return new Date(src.getTime());
	};

	var nextMonth = exports.nextMonth = function nextMonth(src) {
	  var year = src.getFullYear();
	  var month = src.getMonth();
	  var date = src.getDate();

	  var newYear = month === 11 ? year + 1 : year;
	  var newMonth = month === 11 ? 0 : month + 1;

	  var newMonthDayCount = getDayCountOfMonth(newYear, newMonth);
	  if (newMonthDayCount < date) {
	    src.setDate(newMonthDayCount);
	  }

	  src.setMonth(newMonth);
	  src.setFullYear(newYear);

	  return new Date(src.getTime());
	};

	var getRangeHours = exports.getRangeHours = function getRangeHours(ranges) {
	  var hours = [];
	  var disabledHours = [];

	  (ranges || []).forEach(function (range) {
	    var value = range.map(function (date) {
	      return date.getHours();
	    });

	    disabledHours = disabledHours.concat(newArray(value[0], value[1]));
	  });

	  if (disabledHours.length) {
	    for (var i = 0; i < 24; i++) {
	      hours[i] = disabledHours.indexOf(i) === -1;
	    }
	  } else {
	    for (var _i = 0; _i < 24; _i++) {
	      hours[_i] = false;
	    }
	  }

	  return hours;
	};

	var limitRange = exports.limitRange = function limitRange(date, ranges) {
	  if (!ranges || !ranges.length) return date;

	  var len = ranges.length;
	  var format = 'HH:mm:ss';

	  date = _date2.default.parse(_date2.default.format(date, format), format);
	  for (var i = 0; i < len; i++) {
	    var range = ranges[i];
	    if (date >= range[0] && date <= range[1]) {
	      return date;
	    }
	  }

	  var maxDate = ranges[0][0];
	  var minDate = ranges[0][0];

	  ranges.forEach(function (range) {
	    minDate = new Date(Math.min(range[0], minDate));
	    maxDate = new Date(Math.max(range[1], maxDate));
	  });

	  return date < minDate ? minDate : maxDate;
	};

/***/ },

/***/ 54:
/***/ function(module, exports) {

	module.exports = require("element-ui/lib/utils/date");

/***/ },

/***/ 55:
/***/ function(module, exports) {

	module.exports = require("element-ui/lib/utils/vue-popper");

/***/ },

/***/ 56:
/***/ function(module, exports) {

	module.exports={render:function (){with(this) {
	  return _h('span', {
	    directives: [{
	      name: "clickoutside",
	      rawName: "v-clickoutside",
	      value: (handleClose),
	      expression: "handleClose"
	    }],
	    staticClass: "el-date-editor",
	    class: {
	      'is-have-trigger': haveTrigger,
	      'is-active': pickerVisible,
	      'is-filled': !!this.value
	    }
	  }, [_h('input', {
	    directives: [{
	      name: "model",
	      rawName: "v-model.lazy",
	      value: (visualValue),
	      expression: "visualValue",
	      modifiers: {
	        "lazy": true
	      }
	    }],
	    ref: "reference",
	    staticClass: "el-date-editor__editor",
	    attrs: {
	      "readonly": readonly,
	      "type": "text",
	      "placeholder": placeholder
	    },
	    domProps: {
	      "value": _s(visualValue)
	    },
	    on: {
	      "focus": handleFocus,
	      "blur": handleBlur,
	      "keydown": handleKeydown,
	      "change": function($event) {
	        visualValue = $event.target.value
	      }
	    }
	  }), (haveTrigger) ? _h('span', {
	    staticClass: "el-date-editor__trigger el-icon",
	    class: [triggerClass],
	    on: {
	      "click": function($event) {
	        pickerVisible = !pickerVisible
	      }
	    }
	  }) : _e()])
	}},staticRenderFns: []}

/***/ },

/***/ 59:
/***/ function(module, exports) {

	module.exports = require("element-ui/lib/mixins/locale");

/***/ },

/***/ 60:
/***/ function(module, exports, __webpack_require__) {

	var __vue_exports__, __vue_options__

	/* script */
	__vue_exports__ = __webpack_require__(61)

	/* template */
	var __vue_template__ = __webpack_require__(65)
	__vue_options__ = __vue_exports__ = __vue_exports__ || {}
	if (
	  typeof __vue_exports__.default === "object" ||
	  typeof __vue_exports__.default === "function"
	) {
	__vue_options__ = __vue_exports__ = __vue_exports__.default
	}
	if (typeof __vue_options__ === "function") {
	  __vue_options__ = __vue_options__.options
	}

	__vue_options__.render = __vue_template__.render
	__vue_options__.staticRenderFns = __vue_template__.staticRenderFns

	module.exports = __vue_exports__


/***/ },

/***/ 61:
/***/ function(module, exports, __webpack_require__) {

	'use strict';

	exports.__esModule = true;

	var _util = __webpack_require__(53);

	var _locale = __webpack_require__(59);

	var _locale2 = _interopRequireDefault(_locale);

	function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//

	exports.default = {
	  mixins: [_locale2.default],

	  components: {
	    TimeSpinner: __webpack_require__(62)
	  },

	  props: {
	    pickerWidth: {},
	    date: {
	      default: function _default() {
	        return new Date();
	      }
	    },
	    visible: Boolean
	  },

	  watch: {
	    visible: function visible(val) {
	      this.currentVisible = val;
	    },
	    pickerWidth: function pickerWidth(val) {
	      this.width = val;
	    },
	    value: function value(newVal) {
	      var date = void 0;
	      if (newVal instanceof Date) {
	        date = (0, _util.limitRange)(newVal, this.selectableRange);
	      } else if (!newVal) {
	        date = new Date();
	      }

	      this.handleChange({
	        hours: date.getHours(),
	        minutes: date.getMinutes(),
	        seconds: date.getSeconds()
	      });
	    },
	    selectableRange: function selectableRange(val) {
	      this.$refs.spinner.selectableRange = val;
	    }
	  },

	  data: function data() {
	    return {
	      format: 'HH:mm:ss',
	      value: '',
	      hours: 0,
	      minutes: 0,
	      seconds: 0,
	      selectableRange: [],
	      currentDate: this.$options.defaultValue || this.date || new Date(),
	      currentVisible: this.visible || false,
	      width: this.pickerWidth || 0
	    };
	  },


	  computed: {
	    showSeconds: function showSeconds() {
	      return (this.format || '').indexOf('ss') !== -1;
	    }
	  },

	  methods: {
	    handleCancel: function handleCancel() {
	      this.$emit('pick', null);
	    },
	    handleChange: function handleChange(date) {
	      if (date.hours !== undefined) {
	        this.currentDate.setHours(date.hours);
	        this.hours = this.currentDate.getHours();
	      }
	      if (date.minutes !== undefined) {
	        this.currentDate.setMinutes(date.minutes);
	        this.minutes = this.currentDate.getMinutes();
	      }
	      if (date.seconds !== undefined) {
	        this.currentDate.setSeconds(date.seconds);
	        this.seconds = this.currentDate.getSeconds();
	      }

	      this.handleConfirm(true);
	    },
	    setSelectionRange: function setSelectionRange(start, end) {
	      this.$emit('select-range', start, end);
	    },
	    handleConfirm: function handleConfirm() {
	      var visible = arguments.length <= 0 || arguments[0] === undefined ? false : arguments[0];
	      var first = arguments[1];

	      if (first) return;
	      var date = new Date((0, _util.limitRange)(this.currentDate, this.selectableRange));
	      this.$emit('pick', date, visible, first);
	    },
	    ajustScrollTop: function ajustScrollTop() {
	      return this.$refs.spinner.ajustScrollTop();
	    }
	  },

	  created: function created() {
	    this.hours = this.currentDate.getHours();
	    this.minutes = this.currentDate.getMinutes();
	    this.seconds = this.currentDate.getSeconds();
	  },
	  mounted: function mounted() {
	    var _this = this;

	    this.$nextTick(function () {
	      return _this.handleConfirm(true, true);
	    });
	  }
	};

/***/ },

/***/ 62:
/***/ function(module, exports, __webpack_require__) {

	var __vue_exports__, __vue_options__

	/* script */
	__vue_exports__ = __webpack_require__(63)

	/* template */
	var __vue_template__ = __webpack_require__(64)
	__vue_options__ = __vue_exports__ = __vue_exports__ || {}
	if (
	  typeof __vue_exports__.default === "object" ||
	  typeof __vue_exports__.default === "function"
	) {
	__vue_options__ = __vue_exports__ = __vue_exports__.default
	}
	if (typeof __vue_options__ === "function") {
	  __vue_options__ = __vue_options__.options
	}

	__vue_options__.render = __vue_template__.render
	__vue_options__.staticRenderFns = __vue_template__.staticRenderFns

	module.exports = __vue_exports__


/***/ },

/***/ 63:
/***/ function(module, exports, __webpack_require__) {

	'use strict';

	exports.__esModule = true;

	var _util = __webpack_require__(53);

	exports.default = {
	  props: {
	    hours: {
	      type: Number,
	      default: 0
	    },

	    minutes: {
	      type: Number,
	      default: 0
	    },

	    seconds: {
	      type: Number,
	      default: 0
	    },

	    showSeconds: {
	      type: Boolean,
	      default: true
	    }
	  },

	  watch: {
	    hoursPrivate: function hoursPrivate(newVal, oldVal) {
	      if (!(newVal >= 0 && newVal <= 23)) {
	        this.hoursPrivate = oldVal;
	      }
	      this.$refs.hour.scrollTop = Math.max(0, (this.hoursPrivate - 2.5) * 32 + 80);
	      this.$emit('change', { hours: newVal });
	    },
	    minutesPrivate: function minutesPrivate(newVal, oldVal) {
	      if (!(newVal >= 0 && newVal <= 59)) {
	        this.minutesPrivate = oldVal;
	      }
	      this.$refs.minute.scrollTop = Math.max(0, (this.minutesPrivate - 2.5) * 32 + 80);
	      this.$emit('change', { minutes: newVal });
	    },
	    secondsPrivate: function secondsPrivate(newVal, oldVal) {
	      if (!(newVal >= 0 && newVal <= 59)) {
	        this.secondsPrivate = oldVal;
	      }
	      this.$refs.second.scrollTop = Math.max(0, (this.secondsPrivate - 2.5) * 32 + 80);
	      this.$emit('change', { seconds: newVal });
	    }
	  },

	  computed: {
	    hoursList: function hoursList() {
	      return (0, _util.getRangeHours)(this.selectableRange);
	    }
	  },

	  data: function data() {
	    return {
	      hoursPrivate: 0,
	      minutesPrivate: 0,
	      secondsPrivate: 0,
	      selectableRange: []
	    };
	  },


	  methods: {
	    handleClick: function handleClick(type, value, disabled) {
	      if (value.disabled) {
	        return;
	      }

	      this[type + 'Private'] = value.value >= 0 ? value.value : value;

	      this.emitSelectRange(type);
	    },
	    emitSelectRange: function emitSelectRange(type) {
	      if (type === 'hours') {
	        this.$emit('select-range', 0, 2);
	      } else if (type === 'minutes') {
	        this.$emit('select-range', 3, 5);
	      } else if (type === 'seconds') {
	        this.$emit('select-range', 6, 8);
	      }
	    },
	    handleScroll: function handleScroll(type) {
	      var ajust = {};

	      ajust[type + 's'] = Math.min(Math.floor((this.$refs[type].scrollTop - 80) / 32 + 3), 59);
	      this.$emit('change', ajust);
	    },
	    ajustScrollTop: function ajustScrollTop() {
	      this.$refs.hour.scrollTop = Math.max(0, (this.hours - 2.5) * 32 + 80);
	      this.$refs.minute.scrollTop = Math.max(0, (this.minutes - 2.5) * 32 + 80);
	      this.$refs.second.scrollTop = Math.max(0, (this.seconds - 2.5) * 32 + 80);
	    }
	  }
	}; //
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//

/***/ },

/***/ 64:
/***/ function(module, exports) {

	module.exports={render:function (){with(this) {
	  return _h('div', {
	    staticClass: "el-time-spinner"
	  }, [_h('div', {
	    ref: "hour",
	    staticClass: "el-time-spinner__wrapper",
	    on: {
	      "mouseenter": function($event) {
	        emitSelectRange('hours')
	      },
	      "mousewheel": function($event) {
	        handleScroll('hour')
	      }
	    }
	  }, [_h('ul', {
	    staticClass: "el-time-spinner__list"
	  }, [_l((hoursList), function(disabled, hour) {
	    return _h('li', {
	      staticClass: "el-time-spinner__item",
	      class: {
	        'active': hour === hours, 'disabled': disabled
	      },
	      attrs: {
	        "track-by": "hour"
	      },
	      domProps: {
	        "textContent": _s(hour)
	      },
	      on: {
	        "click": function($event) {
	          handleClick('hours', {
	            value: hour,
	            disabled: disabled
	          }, true)
	        }
	      }
	    })
	  })])]), _h('div', {
	    ref: "minute",
	    staticClass: "el-time-spinner__wrapper",
	    on: {
	      "mouseenter": function($event) {
	        emitSelectRange('minutes')
	      },
	      "mousewheel": function($event) {
	        handleScroll('minute')
	      }
	    }
	  }, [_h('ul', {
	    staticClass: "el-time-spinner__list"
	  }, [_l((60), function(minute, key) {
	    return _h('li', {
	      staticClass: "el-time-spinner__item",
	      class: {
	        'active': key === minutes
	      },
	      domProps: {
	        "textContent": _s(key)
	      },
	      on: {
	        "click": function($event) {
	          handleClick('minutes', key, true)
	        }
	      }
	    })
	  })])]), _h('div', {
	    directives: [{
	      name: "show",
	      rawName: "v-show",
	      value: (showSeconds),
	      expression: "showSeconds"
	    }],
	    ref: "second",
	    staticClass: "el-time-spinner__wrapper",
	    on: {
	      "mouseenter": function($event) {
	        emitSelectRange('seconds')
	      },
	      "mousewheel": function($event) {
	        handleScroll('second')
	      }
	    }
	  }, [_h('ul', {
	    staticClass: "el-time-spinner__list"
	  }, [_l((60), function(second, key) {
	    return _h('li', {
	      staticClass: "el-time-spinner__item",
	      class: {
	        'active': key === seconds
	      },
	      domProps: {
	        "textContent": _s(key)
	      },
	      on: {
	        "click": function($event) {
	          handleClick('seconds', key, true)
	        }
	      }
	    })
	  })])])])
	}},staticRenderFns: []}

/***/ },

/***/ 65:
/***/ function(module, exports) {

	module.exports={render:function (){with(this) {
	  return _h('transition', {
	    attrs: {
	      "name": "md-fade-bottom"
	    },
	    on: {
	      "after-leave": function($event) {
	        $emit('dodestroy')
	      }
	    }
	  }, [_h('div', {
	    directives: [{
	      name: "show",
	      rawName: "v-show",
	      value: (currentVisible),
	      expression: "currentVisible"
	    }],
	    staticClass: "el-time-panel",
	    style: ({
	      width: width + 'px'
	    })
	  }, [_h('div', {
	    staticClass: "el-time-panel__content"
	  }, [_h('time-spinner', {
	    ref: "spinner",
	    attrs: {
	      "show-seconds": showSeconds,
	      "hours": hours,
	      "minutes": minutes,
	      "seconds": seconds
	    },
	    on: {
	      "change": handleChange,
	      "select-range": setSelectionRange
	    }
	  })]), _h('div', {
	    staticClass: "el-time-panel__footer"
	  }, [_h('button', {
	    staticClass: "el-time-panel__btn cancel",
	    attrs: {
	      "type": "button"
	    },
	    on: {
	      "click": handleCancel
	    }
	  }, [_s($t('el.datepicker.cancel'))]), _h('button', {
	    staticClass: "el-time-panel__btn confirm",
	    attrs: {
	      "type": "button"
	    },
	    on: {
	      "click": function($event) {
	        handleConfirm()
	      }
	    }
	  }, [_s($t('el.datepicker.confirm'))])])])])
	}},staticRenderFns: []}

/***/ },

/***/ 273:
/***/ function(module, exports, __webpack_require__) {

	'use strict';

	var TimePicker = __webpack_require__(274);

	/* istanbul ignore next */
	TimePicker.install = function (Vue) {
	  Vue.component(TimePicker.name, TimePicker);
	};

	module.exports = TimePicker;

/***/ },

/***/ 274:
/***/ function(module, exports, __webpack_require__) {

	'use strict';

	exports.__esModule = true;

	var _picker = __webpack_require__(50);

	var _picker2 = _interopRequireDefault(_picker);

	var _time = __webpack_require__(60);

	var _time2 = _interopRequireDefault(_time);

	var _timeRange = __webpack_require__(275);

	var _timeRange2 = _interopRequireDefault(_timeRange);

	function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

	exports.default = {
	  mixins: [_picker2.default],

	  name: 'ElTimePicker',

	  props: {
	    isRange: Boolean
	  },

	  created: function created() {
	    this.type = this.isRange ? 'timerange' : 'time';
	    this.panel = this.isRange ? _timeRange2.default : _time2.default;
	  }
	};

/***/ },

/***/ 275:
/***/ function(module, exports, __webpack_require__) {

	var __vue_exports__, __vue_options__

	/* script */
	__vue_exports__ = __webpack_require__(276)

	/* template */
	var __vue_template__ = __webpack_require__(277)
	__vue_options__ = __vue_exports__ = __vue_exports__ || {}
	if (
	  typeof __vue_exports__.default === "object" ||
	  typeof __vue_exports__.default === "function"
	) {
	__vue_options__ = __vue_exports__ = __vue_exports__.default
	}
	if (typeof __vue_options__ === "function") {
	  __vue_options__ = __vue_options__.options
	}

	__vue_options__.render = __vue_template__.render
	__vue_options__.staticRenderFns = __vue_template__.staticRenderFns

	module.exports = __vue_exports__


/***/ },

/***/ 276:
/***/ function(module, exports, __webpack_require__) {

	'use strict';

	exports.__esModule = true;

	var _util = __webpack_require__(53);

	var _locale = __webpack_require__(59);

	var _locale2 = _interopRequireDefault(_locale);

	function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//
	//

	var MIN_TIME = (0, _util.parseDate)('00:00:00', 'HH:mm:ss');
	var MAX_TIME = (0, _util.parseDate)('23:59:59', 'HH:mm:ss');
	var isDisabled = function isDisabled(minTime, maxTime) {
	  var minValue = minTime.getHours() * 3600 + minTime.getMinutes() * 60 + minTime.getSeconds();
	  var maxValue = maxTime.getHours() * 3600 + maxTime.getMinutes() * 60 + maxTime.getSeconds();

	  return minValue > maxValue;
	};
	var clacTime = function clacTime(time) {
	  time = Array.isArray(time) ? time : [time];
	  var minTime = time[0] || new Date();
	  var date = new Date();
	  date.setHours(date.getHours() + 1);
	  var maxTime = time[1] || date;

	  if (minTime > maxTime) return clacTime();
	  return { minTime: minTime, maxTime: maxTime };
	};

	exports.default = {
	  mixins: [_locale2.default],

	  components: {
	    TimeSpinner: __webpack_require__(62)
	  },

	  computed: {
	    showSeconds: function showSeconds() {
	      return (this.format || '').indexOf('ss') !== -1;
	    }
	  },

	  props: ['value'],

	  watch: {
	    value: function value(val) {
	      var time = clacTime(val);
	      if (time.minTime === this.minTime && time.maxTime === this.maxTime) {
	        return;
	      }

	      this.handleMinChange({
	        hours: time.minTime.getHours(),
	        minutes: time.minTime.getMinutes(),
	        seconds: time.minTime.getSeconds()
	      });
	      this.handleMaxChange({
	        hours: time.maxTime.getHours(),
	        minutes: time.maxTime.getMinutes(),
	        seconds: time.maxTime.getSeconds()
	      });
	    }
	  },

	  data: function data() {
	    var time = clacTime(this.$options.defaultValue);

	    return {
	      minTime: time.minTime,
	      maxTime: time.maxTime,
	      btnDisabled: isDisabled(time.minTime, time.maxTime),
	      maxHours: time.maxTime.getHours(),
	      maxMinutes: time.maxTime.getMinutes(),
	      maxSeconds: time.maxTime.getSeconds(),
	      minHours: time.minTime.getHours(),
	      minMinutes: time.minTime.getMinutes(),
	      minSeconds: time.minTime.getSeconds(),
	      format: 'HH:mm:ss',
	      visible: false,
	      width: 0
	    };
	  },


	  methods: {
	    handleCancel: function handleCancel() {
	      this.$emit('pick');
	    },
	    handleChange: function handleChange() {
	      this.$refs.minSpinner.selectableRange = [[MIN_TIME, this.maxTime]];
	      this.$refs.maxSpinner.selectableRange = [[this.minTime, MAX_TIME]];
	      this.handleConfirm(true);
	    },
	    handleMaxChange: function handleMaxChange(date) {
	      if (date.hours !== undefined) {
	        this.maxTime.setHours(date.hours);
	        this.maxHours = this.maxTime.getHours();
	      }
	      if (date.minutes !== undefined) {
	        this.maxTime.setMinutes(date.minutes);
	        this.maxMinutes = this.maxTime.getMinutes();
	      }
	      if (date.seconds !== undefined) {
	        this.maxTime.setSeconds(date.seconds);
	        this.maxSeconds = this.maxTime.getSeconds();
	      }
	      this.handleChange();
	    },
	    handleMinChange: function handleMinChange(date) {
	      if (date.hours !== undefined) {
	        this.minTime.setHours(date.hours);
	        this.minHours = this.minTime.getHours();
	      }
	      if (date.minutes !== undefined) {
	        this.minTime.setMinutes(date.minutes);
	        this.minMinutes = this.minTime.getMinutes();
	      }
	      if (date.seconds !== undefined) {
	        this.minTime.setSeconds(date.seconds);
	        this.minSeconds = this.minTime.getSeconds();
	      }

	      this.handleChange();
	    },
	    setMinSelectionRange: function setMinSelectionRange(start, end) {
	      this.$emit('select-range', start, end);
	    },
	    setMaxSelectionRange: function setMaxSelectionRange(start, end) {
	      this.$emit('select-range', start + 11, end + 11);
	    },
	    handleConfirm: function handleConfirm() {
	      var visible = arguments.length <= 0 || arguments[0] === undefined ? false : arguments[0];
	      var first = arguments.length <= 1 || arguments[1] === undefined ? false : arguments[1];

	      var minSelectableRange = this.$refs.minSpinner.selectableRange;
	      var maxSelectableRange = this.$refs.maxSpinner.selectableRange;

	      this.minTime = (0, _util.limitRange)(this.minTime, minSelectableRange);
	      this.maxTime = (0, _util.limitRange)(this.maxTime, maxSelectableRange);

	      if (first) return;
	      this.$emit('pick', [this.minTime, this.maxTime], visible, first);
	    },
	    ajustScrollTop: function ajustScrollTop() {
	      this.$refs.minSpinner.ajustScrollTop();
	      this.$refs.maxSpinner.ajustScrollTop();
	    }
	  },

	  mounted: function mounted() {
	    var _this = this;

	    this.$nextTick(function () {
	      return _this.handleConfirm(true, true);
	    });
	  }
	};

/***/ },

/***/ 277:
/***/ function(module, exports) {

	module.exports={render:function (){with(this) {
	  return _h('transition', {
	    attrs: {
	      "name": "md-fade-bottom"
	    },
	    on: {
	      "after-leave": function($event) {
	        $emit('dodestroy')
	      }
	    }
	  }, [_h('div', {
	    directives: [{
	      name: "show",
	      rawName: "v-show",
	      value: (visible),
	      expression: "visible"
	    }],
	    staticClass: "el-time-range-picker el-picker-panel",
	    style: ({
	      width: width + 'px'
	    })
	  }, [_h('div', {
	    staticClass: "el-time-range-picker__content"
	  }, [_h('div', {
	    staticClass: "el-time-range-picker__cell"
	  }, [_h('div', {
	    staticClass: "el-time-range-picker__header"
	  }, [_s($t('el.datepicker.startTime'))]), _h('div', {
	    staticClass: "el-time-range-picker__body el-time-panel__content"
	  }, [_h('time-spinner', {
	    ref: "minSpinner",
	    attrs: {
	      "show-seconds": showSeconds,
	      "hours": minHours,
	      "minutes": minMinutes,
	      "seconds": minSeconds
	    },
	    on: {
	      "change": handleMinChange,
	      "select-range": setMinSelectionRange
	    }
	  })])]), _h('div', {
	    staticClass: "el-time-range-picker__cell"
	  }, [_h('div', {
	    staticClass: "el-time-range-picker__header"
	  }, [_s($t('el.datepicker.endTime'))]), _h('div', {
	    staticClass: "el-time-range-picker__body el-time-panel__content"
	  }, [_h('time-spinner', {
	    ref: "maxSpinner",
	    attrs: {
	      "show-seconds": showSeconds,
	      "hours": maxHours,
	      "minutes": maxMinutes,
	      "seconds": maxSeconds
	    },
	    on: {
	      "change": handleMaxChange,
	      "select-range": setMaxSelectionRange
	    }
	  })])])]), _h('div', {
	    staticClass: "el-time-panel__footer"
	  }, [_h('button', {
	    staticClass: "el-time-panel__btn cancel",
	    attrs: {
	      "type": "button"
	    },
	    on: {
	      "click": function($event) {
	        handleCancel()
	      }
	    }
	  }, [_s($t('el.datepicker.cancel'))]), _h('button', {
	    staticClass: "el-time-panel__btn confirm",
	    attrs: {
	      "type": "button",
	      "disabled": btnDisabled
	    },
	    on: {
	      "click": function($event) {
	        handleConfirm()
	      }
	    }
	  }, [_s($t('el.datepicker.confirm'))])])])])
	}},staticRenderFns: []}

/***/ }

/******/ });