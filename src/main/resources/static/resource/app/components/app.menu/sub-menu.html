<el-menu-item v-if="!item.children" :index="index" :is-first-level="isRootMenu">
    <div>
        <i class="fa" v-if="item.icon && item.icon.length>0" :class="item.icon"></i><a class="el-menu-item__title-label"><span style="width:140px;">{{item.name}}</span></a>
    </div>
</el-menu-item>
<el-submenu v-else :index="index" :is-first-level="isRootMenu"  :narrow-class="narrowClass">
    <template slot="title">
        <i class="fa" v-if="item.icon && item.icon.length>0" :class="item.icon" style="vertical-align: top"></i><span class="el-submenu__title-label" style="width:140px;">{{item.name}}</span>
    </template>
    <sub-menu v-for="(subItem,subIndex) in item.children" :item='subItem' :index='index +"-"+ subIndex'></sub-menu>
</el-submenu>
