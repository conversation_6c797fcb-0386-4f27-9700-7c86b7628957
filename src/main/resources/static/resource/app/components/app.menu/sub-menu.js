define(['text!./components/app.menu/sub-menu.html','vue','ELEMENT'],function(html,Vue,ELEMENT){
    Vue.component('sub-menu',{
        template:html,
        props: {
            item: {
                type:Object,
                default:function(){
                    return {};
                }
            },
            index: String,
            isRootMenu: Boolean
        },
        methods:{
            menuClick:function(){
                this.rootMenu.$emit('menu-open');
            }
        },
        computed: {
            narrowClass:function(){
                if(this.isRootMenu){
                    return "menu-popper";
                }
                return "";
            }
        },
        created:function(){
            var parent = this.$parent;

            if (this.isRootMenu) {
                this.rootMenu = parent.$parent.$parent;
            } else {
                this.rootMenu = parent.$parent.rootMenu;
            }

            var item = this.$options.propsData.item;
            $.hashAjax.register({
                method:'get',
                link:item.link,
                path: this.$options.propsData.index,
                type:'html',
                callback:function(data) {
                    $("#content .page-content-container").html(data);
                    $("#content .page-content-loading").hide();
                }
            });
        }
    });

})