/**
 * Created by will.song on 2016/11/3.
 */
define(['text!./components/shang.tabs/index.html','vue'],function(html,Vue){
    Vue.component('shang-tabs', {
        template: html,
        data: function () {
            return {
                selectedIndex: 0
            }
        },
        props: {
            source: {
                type:Array,
                default:function(){return []}
            }
        },
        methods: {
            tabClick:function(e,index){
                e.preventDefault();
                this.source.splice(index+1);
                this.$emit('item-click',this.source[index]);
            }
        },
        watch:{
            source:function(val){
                this.selectedIndex= val.length-1;
            }
        }

    });
});
