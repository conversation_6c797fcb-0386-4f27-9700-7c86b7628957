define(['jquery'],function($){
    var printAreaCount = 0;
    $.fn.printArea = function() {
        var ele = $(this);
        var idPrefix = "printArea_";
        $("iframe._printArea_iframe_").remove();

        printAreaCount++;
        var iframeId = idPrefix + printAreaCount;
        var iframeStyle = 'position:absolute;width:0px;height:0px;left:-500px;top:-500px;';

        iframe = document.createElement('IFRAME');
        $(iframe).attr({style : iframeStyle,id : iframeId,"class":"_printArea_iframe_"});
        document.body.appendChild(iframe);

        var doc = iframe.contentWindow.document;
        $(document).find("link").filter(function() {
            return $(this).attr("rel").toLowerCase() == "stylesheet";
        }).each(
            function() {
                doc.write('<link type="text/css" rel="stylesheet" href="'+ $(this).attr("href") + '" >');
            }
        );
        doc.write('<div class="' + $(ele).attr("class") + '">' + $(ele).html()+ '</div>');
        doc.close();

        var frameWindow = document.getElementById(iframeId).contentWindow;
        frameWindow.close();
        frameWindow.focus();
        setTimeout(function(){
            frameWindow.print();
        },200);


        return  iframeId
    }
})