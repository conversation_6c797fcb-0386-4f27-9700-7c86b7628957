define(['text!./components/app.memberselect/memberselect.html','vue','ELEMENT'],function(html,Vue,ELEMENT){
    Vue.component('shang-member-select',{
        template:html,
        props: {
            placeholder:{
                type:String,
                default:''
            },

            disabled:Boolean,

            title:String,
        },
        data:function () {
            return {
                uuid:('' + Math.random()).split('.')[1],
                uuid2:('' + Math.random()).split('.')[1],

                confirmText: XiaI18n("js.par001.confirm"),

                cancelText: XiaI18n("js_dropzone_cancel"),

                dialogVisible:false,

                treeJson:[],

                sel:null,

                selectedPersons:[],


            }

        },
        methods:{
            openDialog:function () {
                var me = this;
                if(me.disabled){
                    return;
                }
                me.dialogVisible = true;
                me.$nextTick(function () {
                    me.addMemberSelect();
                });
            },

            addMemberSelect:function () {
                var me = this;

                me.sel = $('#' + this.uuid).memberSelect({
                    ajax: {
                        url: '/common/tabledata/person/{id}_{type}',
                        params: ['id','type'],
                        dataKey:'id',
                        initParams: [getCurrentProject(),2],
                        method: 'POST'
                    },
                    returnFields: ['id', 'name', 'type', 'path', 'projectName','proId'],
                    dataKey: 'id',
                    selectionRender: function (item) {
                        if (getCurrentProject() == 0) {
                            if (item.projectName != undefined && item.projectName != null) {
                                return "<b>" + item.projectName + "</b>" + ":" + item.name;
                            } else {
                                return item.name;
                            }
                        } else {
                            return item.name;
                        }
                    },
                    values: me.treeJson
                });
            },

            setLabels:function () {
                var html_ = "";

                for (var i = 0; i < this.selectedPersons.length; i++) {
                    var data = "";
                    if (getCurrentProject() == 0) {
                        if (this.selectedPersons[i].projectName != undefined &&
                            this.selectedPersons[i].projectName != null) {
                            data += "<b>" + $.escapeHtml(this.selectedPersons[i].projectName) + "</b>:" + $.escapeHtml(this.selectedPersons[i].name);
                        } else {
                            data += $.escapeHtml(this.selectedPersons[i].name);
                        }
                    } else {
                        data += $.escapeHtml(this.selectedPersons[i].name);
                    }


                    html_ += "<label style='background-color: #eee;display: inline-block;padding: 3px;margin: 0px 3px 3px 0px;'>" + (data) + "</label>";

                }

                $('#' + this.uuid2).html(html_);
                if(html_===''){
                    $("#" + this.uuid2).append('<span style="color: #aaa;line-height: 28px;padding-left:3px;">'+this.placeholder+'</span>');
                }
            },

            confirmSelect:function () {
                this.treeJson = this.sel.getJSONValues();
                this.selectedPersons = this.sel.getValues();
                console.log(this.selectedPersons);

                this.setLabels();

                this.dialogVisible = false;
            },

            /*
            * 设置默认的值
            * treeJson对应后台交互的值
            * selectedPersons即为treejson的扁平化
            * */
            setDefault:function (selectedPersons) {
                this.treeJson = selectedPersons;
                this.selectedPersons = selectedPersons;
                this.setLabels();
            },

            /*
            * 获取交互的json
            * */
            getSelected:function () {
                return this.selectedPersons;
            }
        },

        watch:{
            disabled:function () {
                if(this.disabled){
                    $("#"+this.uuid2).attr('disabled', true);
                    $("#"+this.uuid2).css('background-color','#f0f4f5');
                    $("#"+this.uuid2).css('border-color','#f0f4f5');
                    $("#"+this.uuid2).css('color','#666');
                    $("#"+this.uuid2).css('cursor','not-allowed');
                }else {
                    $("#"+this.uuid2).attr('disabled', false);
                    $("#"+this.uuid2).css('background-color','#fff');
                    $("#"+this.uuid2).css('border-color','#f0f4f5');
                    $("#"+this.uuid2).css('cursor','');

                }
            }
        },
        mounted : function () {
            if(this.disabled){
                $("#"+this.uuid2).attr('disabled', true);
                $("#"+this.uuid2).css('background-color','#f0f4f5');
                $("#"+this.uuid2).css('border-color','#f0f4f5');
                $("#"+this.uuid2).css('color','#666');
                $("#"+this.uuid2).css('cursor','not-allowed');

                }
        }
    });

})