/**
 * 依赖 jquery.js datatables.js
 *
 * @link http://datatables.net
 *
 */
define(['datatables','datatables.tools','datatables.locale','XiaDateRange'],function(dt, dtTools, dataTableLanguage){

	var locale=document.documentElement.lang;
	$.fn.dataTable.defaults.language = $.extend(true, $.fn.dataTable.defaults.language, $.fn.dataTable.defaults.regional[locale]);

	var XiaTableV2 = function(containerId, params) {
		var dataTableLanguage = $.fn.dataTable.defaults.language;
		var defaultParams = {
			// 数据
			"processing" : true,// 加载中提示
			"serverSide" : true,// 开启服务器访问 同时要设置ajax参数
			"ajax" : {// 设置数据源
				"type" : "post",// 默认get方式
				"contentType" : "application/json",
				"processData" : false
			},

			// 分页
			"paging" : true,// 显示分页选择
			"lengthChange" : false,// 不显示左上角的页面数据条数改变
			"pageLength" : 10,// 默认显示10条
			// 搜索
			"searching" : false,// 右上角的全局搜索

			// 排序
			"ordering" : false,// 标题栏显示排序，default 全部字段，具体关闭在columns.orderable=false
			"info" : false,// 显示页数据信息
			"pagingType" : "simple_numbers",// 分页类型，simple|default=simple_numbers|full|full_numbers

			// toolbar
			"dom" : 'lfrtip',

			// f1参数
			"isTableFoot" : false,// 是否显示尾部title
			"checkall" : {
				"title" : "&nbsp;",
				"name" : "",// 指定名
				"value" : ""// 【必填】，指定值
				// "data":[{"title","url","class"}],//【必填】
			},
			"operate" : {
				"title" : dataTableLanguage["operate"],
				/*
				 * "data":[//【必填】 {"title","url","class"}, ]
				 */
				// "width":"";
			},
			"createdRow" : function(row, data, index) {
				$(row).attr("data-row", index);
			},
			"separator" : ",",
			// "toolbar":[ {"title":"","class":""}],工具条
			"isColumnsSearch" : false,// 是否开启搜索，default
			// 全部字段，具体关闭在columns.searchable=false
			// "onComplete":fun, 在datatable渲染完后触发
			// "conditions":[{"field","","opt":"","values":[]}] 查询
			// "repository":""repository
			// "ireload":自定义查询接收参数对象
			"toolbarR" : {
				output : {
					"title" : "导出",
					"class" : "",
					tpl : "",
					url : "/data-extraction/export",
					restrict : "111"
				}
			},
			"onNoRowSelected":null,//没有行选中时
		};
		var tableId = containerId + "_xia_table";
		var wrapperId =tableId+'_wrapper';
		var data_checkall = containerId + "_checkall";
		var data_operate = containerId + "_operate";
		var formateDateColumn = new Array();
		var formateType = new Array();
		var longDataColumn = new Array();
		var datatable;
		var data_columns_length = params["columns"].length;
		var rowSelectedOptions;
		/**
		 * 创建table
		 */
		var create_table = function() {
			var table = '<table id="' + tableId
				+ '" class="display" cellspacing="0" width="100%"></table>';
			$("#" + containerId).html(table);
		}
		var getIndexNoHide = function(i){
			var _index = -1;
			if(i>=0 && i<params.columns.length){
				for(var index in params.columns){
					if(params.columns[index].visible === false){
						continue;
					}else{
						_index ++;
					}
					if(_index == i){
						_index = index;
						break;
					}
				}
			}
			return _index;
		}
		/**
		 * 添加搜索行
		 */
		var add_search_input = function() {
			var _wrapperId = tableId;
			if(params["scrollX"] || params["scrollY"]){
				_wrapperId = wrapperId +" .dataTables_scrollHead ";
			}
			if($('#' + _wrapperId).find(".table-search").length){
				if($("#"+tableId).parent().hasClass("dataTables_scrollBody")){
					$("#"+tableId).find(".table-search").hide();
				}
				return;
			}

			var start = -1;
			var end = $('#' + _wrapperId + ' thead th').size();
			if (undefined != params.checkall) {// 如果有checkall列
				start = 0;
			}
			if (undefined != params.operate) {// 如果有operate列
				end = end - 1;
			}
			var html = "<tr class='table-search'>";
			var _wrapperHeadThs = $('#' + _wrapperId + ' thead th');
			_wrapperHeadThs.each(function(i) {
				var title = _wrapperHeadThs.eq($(this).index()).text();
				var thindex = getIndexNoHide(i);
				//字段列
				if (i > start&& i < end&& (undefined == params.columns[thindex].searchable || params.columns[thindex].searchable)) {// 0 and size-1
					//增加验证
					var _valids = " ";
					if(params.columns[thindex].valids){
						for(var _validIndex in valids){
							_valids += _validIndex+"='"+valids[_validIndex]+"' ";
						}
					}
					//判断搜索方式
					//选择框
					if(params.columns[thindex].changeValue || (params.columns[thindex].search && params.columns[thindex].search.dataSelect)){
						var cv = params.columns[thindex].changeValue;
						if(params.columns[thindex].search && params.columns[thindex].search.dataSelect){
							cv = params.columns[thindex].search.dataSelect;
						}
						var select = "<select  class='"+containerId+"_columnSearchInput' style='width:100%;border:1px solid #ddd;'  "+($.trim(_valids)?_valids:"")+">";
						for(var cv_k in cv){
							select+="<option value='"+cv_k+"'>"+cv[cv_k]+"</option>";
						}
						select+="</select>";

						html+="<td>"+select+"</td>";
					}else{
						var i_class = " ";
						if(params.columns[thindex].search && params.columns[thindex].search.dataType=="date"){
							i_class="datatable-datetime";
						}else if(params.columns[thindex].search && params.columns[thindex].search.dataType=="daterange"){
							i_class="datatable-daterangetime";
						}
						html += '<td class="center"><input  class="'+ containerId+ '_columnSearchInput '+i_class+'" type="text" placeholder=" '+ title+ '" style="width:100%" data-p-i="'+thindex+'" '+($.trim(_valids)?_valids:"")+'/><i class="t-search"></i></td>';
					}
				} else {//其它列
					html += '<td><input class="'+ containerId+ '_columnSearchInput" type="hidden" /></td>';
				}
			});
			html += "</tr>";

			$('#' + _wrapperId + ' thead').append(html);

			var table = $("#" + tableId).DataTable();
			var inputSearchButton = function(obj){
				$(obj).next().removeClass("t-search");
				$(obj).next().addClass("t-remove");
			}
			//控制搜索图标和X图标

			$("#"+_wrapperId+" ." + containerId + "_columnSearchInput").each(function(i) {
				//多条件搜索
				$(this).on("keyup", function(event) {
					if($.trim($(this).val())){
						inputSearchButton(this);
					}
					//退格
					if(event.keyCode == 8){
						if(!$(this).val()){
							$(this).next().removeClass("t-remove");
							$(this).next().addClass("t-search");
						}
					}
				});
				//X删除事件
				$(this).parent().find(".t-search").click(function(){
					if($(this).hasClass("t-remove")){
						var inpt = $(this).parent().find("input");
						inpt.val("");
						inpt.attr("title") && inpt.attr("title","");

						$(this).removeClass("t-remove");
						$(this).addClass("t-search");
						table.draw();
					}
				});
			});
			//输入框Enter事件
			$("#" + _wrapperId).find(".table-search input").keydown(function(event) {
				if (event.keyCode == 13) {
					table.draw();
				}
			});
			//select改变事件
			$("#" + _wrapperId).find(".table-search select").change(function(){
				table.draw();
			});
			//日历事件
			//TODO
			$("#" + _wrapperId).find(".table-search .datatable-datetime").attr("readonly",true);
			$("#" + _wrapperId).find(".table-search .datatable-daterangetime").attr("readonly",true);
			$("#" + _wrapperId).find(".table-search .datatable-datetime").each(function(){
				var i = $(this).attr("data-p-i");
				var _objDateTime = $(this);
				var _objDateTimeNext = $(this).next().next();
				var _p = {};
				var isNumberOfMonth = false;
				if( params.columns[i].search && params.columns[i].search.dataDate){
					_p = params.columns[i].search.dataDate;
					_p["isClear"] = false;
					if(_p["numberOfMonths"] && _p["numberOfMonths"]==2){
						//2
						_p["onClose"] =function( selectedDate ) {
							var _oldRealSelectedDate = _objDateTime.val().split("~");
							selectedDate = selectedDate==_objDateTime.val()?"":selectedDate;
							selectedDate = selectedDate?(_oldRealSelectedDate[0]+"~"+selectedDate):_objDateTime.val();
							_objDateTime.attr("title",selectedDate);
							_objDateTime.val(selectedDate);

							if(selectedDate){
								inputSearchButton(_objDateTime);
								table.draw();
							}
						}
						_objDateTimeNext.datepicker(_p);
						isNumberOfMonth = true;
					}
				}
				//1
				_p["onClose"] =function( selectedDate ) {
					if(selectedDate && isNumberOfMonth){
						var _realSelectedDate = selectedDate.split("~");
						_objDateTimeNext.val(_realSelectedDate[0]);
						_objDateTimeNext.datepicker( "option", "minDate", _realSelectedDate[0]);
						_objDateTimeNext.focus();
					}else if(selectedDate){
						inputSearchButton(_objDateTime);
						table.draw();
					}
					_objDateTime.attr("title",selectedDate);
				}
				//不需要清理按钮
				_p["isClear"] = false;
				$(this).datepicker(_p);
			});

			$("#" + _wrapperId).find(".table-search .datatable-daterangetime").each(function(){
				new XiaDateRange(this);
				$(this).on('hide.daterangepicker', function(ev, picker) {
					var _daterange = $(this).val();
					if(_daterange){
						inputSearchButton(this);
						table.draw();
					}
					$(this).attr("title",_daterange);
				});
			});
		}
		//title 国际化
		var titleI18n = function(){
			var language = XiaTableV2.language[containerId];
			if(language){
				for(var i in params['columns']){
					params['columns'][i]["title"] = language[params['columns'][i]["data"]];
				}
			}
		}
		/**
		 * 处理“操作”参数
		 *
		 */
		var do_params = function() {
			//title 国际化
			titleI18n();

			params["ajax"] = params["ajax"] ? params["ajax"] : {};
			// 处理实体查询
			if (undefined != params["repository"]) {
				params["ajax"]["url"] = "/common/tabledata/" + params["repository"];
			}

			params["columnDefs"] = params["columnDefs"] ? params["columnDefs"]
				: new Array();
			params["dom"] = params["dom"] ? params["dom"] : defaultParams["dom"];
			// 处理dom
			// <"'+containerId+'_toolbar_operate"><"clear">

			// 如果有checkall列
			if (undefined != params["checkall"]) {
				var _columns = new Array();
				var col_checkall = {
					"title" : "<input class='" + containerId + "_checkall"
					+ "' id='checkallInput' type='checkbox' hidden /><label for='checkallInput' class='checkboxdiv'></label>",
					"data" : data_checkall
				};
				if (params["checkall"]["width"]) {
					col_checkall["width"] = params["checkall"]["width"];
				}
				_columns.push(col_checkall);

				for ( var _i in params["columns"]) {
					_columns.push(params["columns"][_i]);
				}

				params["columns"] = _columns;
				// 不允许搜索，排序
				params["columnDefs"].push({
					"searchable" : false,
					"orderable" : false,
					"targets" : 0
				});

				// 定义标题
				if (undefined != params["checkall"]["name"]) {
					var _col_checkbox = {
						"render" : function(data, type, row) {
							return data + ' (' + row[params["checkall"]["name"]]
								+ ')';
						},
						"targets" : 0
					};
					params["columnDefs"].push(_col_checkbox);
				}

			}

			// 定义右操作
			if (undefined != params["toolbarR"]) {
				params["dom"] = '<"' + containerId
					+ '_toolbar_operate_right DTTT_container">' + params["dom"];
			}

			// 定义作操作dom
			if (undefined != params["checkall"]
					//|| undefined != params["isColumnsSearch"]
				|| undefined != params["toolbar"]) {
				params["dom"] = '<"' + containerId
					+ '_toolbar_operate table_toolbar_operate">'
					+ params["dom"];
			}

			// 如果有operate列
			if (undefined != params["operate"]) {
				var _size = params["columns"].length;
				var th_operate = {
					"title" :dataTableLanguage["operate"],
					"data" : data_operate,
					"defaultContent":"",
					"class" : "center"
				};
				if (params["operate"]["width"]) {
					th_operate['width'] = params["operate"]["width"];
				}
				params["columns"].push(th_operate);
				// 不允许搜索，排序
				params["columnDefs"].push({
					"searchable" : false,
					"orderable" : false,
					"targets" : _size
				});
			}

			// reget size
			data_columns_length = params["columns"].length;

			// ajax data
			if (undefined == params["ajax"]["data"]) {
				params["ajax"]["data"] = ajax_data;
			}

			// ajax dataSrc
			//ajax 处理完后的返回值回调函数
			//这里主要用来对返回后的数据做处理
			if (undefined == params["ajax"]["dataSrc"]) {
				params["ajax"]["dataSrc"] = function(json) {
					var data = json["data"];
					//每一行
					for ( var i in data) {
						// 如果有checkall列
						if (undefined != params["checkall"]) {
							data[i][data_checkall] = '<input type="checkbox" class="'+ containerId+ '_checkall_item" id="checkboxInput' + data[i][params["checkall"]["value"]] +'"  value="'+ data[i][params["checkall"]["value"]] + '" style="display:none;">';
							data[i][data_checkall] += '<label class="checkboxdiv" for="checkboxInput' + data[i][params["checkall"]["value"]] +'"></label>';
						}
						// 如果有operate列
						//添加a链接，url值替换，
						if (undefined != params["operate"]) {
							var _o_html = "";
							var _o_d_a = "";
							for ( var _i in params["operate"]["data"]) {
								_o_d_a = Object.clone(params["operate"]["data"][_i]);// 需要深复制对象
								_o_d_a["url"] = do_data_operate_url(data[i],_o_d_a["url"]);
								_o_html += '&nbsp;<a href="'+ (_o_d_a["url"] ? _o_d_a["url"]: "javascript:;")+ '" class="dt_op_a '+ (_o_d_a["class"] ? _o_d_a["class"] : "")+ '">'+ (_o_d_a["title"] ? _o_d_a["title"]: "undefined") + '</a>';
							}
							data[i][data_operate] = _o_html;
						}
						// 对于可能有分隔符
						//多属性值处理
						var separator = params["separator"] ? params["separator"]: defaultParams["separator"];
						for ( var col in params["columns"]) {
							var column = params["columns"][col];
							if (column["data"].split(",").length > 1) {
								var colA = column["data"].split(",");
								var val = "";
								for ( var _col in colA) {
									if (colA[_col].length > 0) {
										// 多级空值问题
										if (colA[_col].split(".").length > 1) {
											var _colA = colA[_col].split(".");
											var _code = "data[" + i + "]";
											for ( var _c in _colA) {
												_code += "." + _colA[_c];
												if (_c == (_colA.length - 1)) {
													if (eval(_code) != null) {
														val += eval(_code)+ separator;
													}
												} else {
													if (!eval(_code + "?true:false")) {
														break;
													}
												}
											}
										} else {
											var _code = "data[" + i + "]."+ colA[_col];
											if (eval(_code) != null) {
												val += eval(_code) + separator;
											}
										}
									}
								}
								data[i][column["data"]] = val.substr(0, val.length- separator.length);
							}
						}

						//状态值显示替换
						for ( var col in params["columns"]) {
							var column = params["columns"][col];
							if (column.changeValue) {
								//data[i][column["data"]] = column.changeValue[data[i][column["data"]]];
								if(!isColumnDataNull(data[i],column["data"])){
									eval("data["+i+"]."+column["data"]+"=column.changeValue["+"data["+i+"]."+column["data"]+"]");
								}
							}
							if(column.search && column.search.dataSelect){
								//data[i][column["data"]] = column.search.dataSelect[data[i][column["data"]]];
								if(!isColumnDataNull(data[i],column["data"])){
									eval("data["+i+"]."+column["data"]+"=column.search.dataSelect["+"data["+i+"]."+column["data"]+"]");
								}
							}
						}
					}

					if (formateDateColumn.length > 0) {
						formatData(data);
					}

					return data;
				}
			}
			var longDataColumnHide = 0;
			for ( var i in params["columns"]) {
				var column = params["columns"][i];
				if (column) {
					// 时间格式化
					if ( params.columns[i].dateFormate === true) {
						formateDateColumn.push(column["data"]);
						formateType
							.push(params.columns[i].dateType != undefined ? params.columns[i].dateType
								: "yyyy-MM-dd");
					}

					// 时间格式化
					if ( params.columns[i].dateTimeFormate === true) {
						formateDateColumn.push(column["data"]);
						formateType
							.push(params.columns[i].dateType != undefined ? params.columns[i].dateType
								: "yyyy-MM-dd HH:mm");
					}

					// 时间格式化
					if ( params.columns[i].timeFormate === true) {
						formateDateColumn.push(column["data"]);
						formateType
							.push(params.columns[i].dateType != undefined ? params.columns[i].dateType
								: "HH:mm:ss");
					}
					// 长文字弹层
					if(params.columns[i].visible === false){
						longDataColumnHide++;
					}else{
						if (params.columns[i].longData != undefined
							&& params.columns[i].longData == true) {
							var _longData = new Object();
							_longData["id"] = i-longDataColumnHide;
							longDataColumn.push(_longData);
						}
					}
				}
			}

			// do on complete页面完成渲染
			// 这里主要用来对ajax成功后做页面处理，
			params["_onComplete"] = function() {
				//search bar
				if (isEnable("isColumnsSearch")) {
					add_search_input();
				}
				//width
				if(params["scrollX"] || params["scrollY"]){
					$("#"+wrapperId +" .dataTables_scrollHeadInner").width("100%");
					$("#"+wrapperId +" .dataTables_scrollHeadInner > table").width("100%");
					$("#"+wrapperId +" .dataTables_scrollBody  > table").width("100%");
				}
				//user complete
				if (undefined != params["onComplete"]) {
					params["onComplete"]();
				}
				//pop
				longDataShow();

				if (undefined != params["onRowClick"]) {
					var table = $("#" + tableId).DataTable();
					var datas = table.data();
					var trs = $("#" + tableId + " > tbody > tr");
					trs.click(function() {
						var index = $(this).attr("data-row");
						params["onRowClick"](datas[index],this);

						if(!params["onRowClickOnlyAdd"]){
							trs.removeClass("selected");
						}
						$(this).addClass("selected");
						//row selec
						var _ajax_row = datatable.data()[$(this).data("row")];
						rowSelectedOptions = _ajax_row ?(_ajax_row["id"]?_ajax_row["id"]:_ajax_row):null;
					});
					// 判断checkall和操作列
					for (var t = 0; t < trs.length; t++) {
						// checkall
						if (undefined != params["checkall"]) {
							var c = trs[t].children;
							c[0].onclick = function(e) {
								e.stopPropagation();
							}
						}
						// operate
						if (undefined != params["operate"]) {
							var l = trs[t].children.length;
							var c = trs[t].children;
							var last_col = $(c[l - 1]);
							last_col.click(function(e) {
								e.stopPropagation();
							});
							// Add By Aaron to support get row data when click
							if(params['operate'].data && params['operate'].data.length !=0){
								params['operate'].data.forEach(function (entry){
									if(entry['class'] && entry['onClick']){
										var index = last_col.parent('tr').attr('data-row');
										last_col.find('.'+entry['class']).click(function(e){
											entry['onClick'](datas[index],e);
											e.stopPropagation();
										});
									}
								});
							}
						}
					}
				}

				//row selecte
				if(rowSelectedOptions){
					setRowSelected();
				}
			}
		}

		var isColumnDataNull = function(data,key){
			var _clumn_data_null = false;
			//不检测，分隔的
			if(key.indexOf(",") == -1){
				var _clumn_data = key.split(".");
				if (_clumn_data.length > 1) {
					var _clumn_data_key = "data";
					for(var _cd_i in _clumn_data){
						_clumn_data_key += "."+_clumn_data[_cd_i]
						if (eval(_clumn_data_key) == null) {
							_clumn_data_null = true;
							break;
						}
					}
				}else{
					if (eval("data."+key) == null) {
						_clumn_data_null = true;
					}
				}
			}
			return _clumn_data_null;
		}

		/**
		 * 发送ajax之前调用
		 * 处理ajax的data参数
		 */
		var ajax_data = function(d) {

			var res = new Object();

			if (d["length"] < 0) {
				res["pageSize"] = params["pageLength"] ? params["pageLength"]
					: defaultParams["pageLength"];
			} else {
				res["pageSize"] = d["length"];
			}
			res["offset"] = d["start"];
			res["pageNumber"] = (d["start"] / d["length"] + 1);
			res["draw"] = d["draw"];
			// res["searchAll"] = d["search"]["value"];
			res["columns"] = new Array();

			//search input data
			if (isEnable("isColumnsSearch")) {// search input
				var _searchInputClass = " ."+containerId + "_columnSearchInput";
				if(params["scrollX"] || params["scrollY"]){
					_searchInputClass = "#"+containerId+" .dataTables_scrollHead "  + _searchInputClass;
				}
				$(_searchInputClass).each(function(i) {
					var thindex = getIndexNoHide(i);
					d["columns"][thindex]["search"]["value"] = $(this).val();
				});
			}
			// 合并order到columns
			for ( var i in d["order"]) {
				var order = d["order"][i];
				d["columns"][order["column"]]["orderValue"] = order["dir"];
			}

			// 删除多余checkall参数
			if (undefined != params["checkall"]) {
				delete d["columns"][0];
				d["columns"][0] = null;
			}

			// 删除多余operate参数
			if (undefined != params["operate"]) {
				delete d["columns"][data_columns_length - 1];
				d["columns"][data_columns_length - 1] = null;
			}
			//搜索、排序
			for ( var i in d["columns"]) {
				var column = d["columns"][i];
				if (column) {
					var _col = new Object();
					_col["name"] = column["data"];
					_col["searchText"] = column["search"]["value"];
					_col["orderType"] = column["orderValue"];
					_col["searchType"] = params["columns"][i]["search"]?params["columns"][i]["search"]["dataType"]:"";
					res["columns"].push(_col);
				}
			}
			// conditions
			if (undefined != params["conditions"]) {
				res["conditions"] = params["conditions"];
			}
			// ireload
			if (undefined != params["ireload"]) {
				res[params["ireload"][0]] = params["ireload"][1];
			}
			return JSON.stringify(res);
		}

		/**
		 * 处理operate 的url中的参数替换
		 */
		var do_data_operate_url = function(data, url) {
			var reg;
			if (url) {
				var urla = url.split(",");
				// for(var _i in data){
				for ( var _o in urla) {
					urla[_o] = $.trim(urla[_o]);
					var _f = urla[_o].substr(1, (urla[_o].length - 2));
					var _v = eval("data." + _f);
					if (undefined != _v) {
						reg = new RegExp(urla[_o]);
						url = url.replace(reg, _v);
					}
				}
				// }
			}
			return url;
		}
		function map() {
			/** 存放键的数组(遍历用到) */
			this.keys = new Array();
			/** 存放数据 */
			this.data = new Object();
			/**
			 * 遍历Map,执行处理函数
			 *
			 * @param {Function}
			 *            回调函数 function(key,value,index){..}
			 */
			this.each = function(fn) {
				if (typeof fn != 'function') {
					return;
				}
				var len = this.keys.length;
				for (var i = 0; i < len; i++) {
					var k = this.keys[i];
					fn(k, this.data[k], i);
				}
			};
			/**
			 * 放入一个键值对
			 *
			 * @param {String}
			 *            key
			 * @param {Object}
			 *            value
			 */
			this.put = function(key, value) {
				if (this.data[key] == null) {
					this.keys.push(key);
				}
				this.data[key] = value;
			};
			/**
			 * 获取某键对应的值
			 *
			 * @param {String}
			 *            key
			 * @return {Object} value
			 */
			this.get = function(key) {
				return this.data[key];
			};
			this.size = function() {
				return this.keys.length;
			};
		}

		/**
		 * 事件格式化 默认格式为yyyy-MM-dd
		 */
		function dataCon(jsonobj, ayyar, index, ins, s) {
			for ( var x in jsonobj) {
				if (x == ayyar[ins - 1]) {
					if (index > ins) {
						ins++;
						return dataCon(jsonobj[x], ayyar, index, ins, s);
					} else {
						if (jsonobj[x] != null) {
							jsonobj[x] = XiaTableV2.dateFormat(jsonobj[x],formateType[s]);
						}
					}
				}
			}
		}

		var formatData = function(d) {
			if (d != null) {
				for ( var s in formateDateColumn) {
					var l = formateDateColumn[s].split('.').length;
					var a = new Array();
					a = formateDateColumn[s].split('.');
					var index = l;
					var ins = 1;
					for (var i = 0; i < d.length; i++) {
						var jsonobj = d[i];
						dataCon(jsonobj, a, index, ins, s);
					}
				}
			}
		}

		function longDataShow() {
			if (longDataColumn.length > 0 && longDataColumn != null) {
				var title = new Array();
				$('#' + tableId + ' thead th').each(function() {
					title.push($(this).text());
				});
				var tr_l = $('#' + tableId + " tbody tr");
				for (var i = 0; i < tr_l.length; i++) {
					var td_l = tr_l.eq(i).find("td");
					for (var s = 0; s < longDataColumn.length; s++) {
						var num_ = longDataColumn[s].id;
						var text = td_l.eq(num_).text();
						var html = "";
						if ($(td_l.eq(num_)).find("input").length == 0) {
							html = "<div class='draggable wodiv pop' style='width:100%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;' data-title='"
								+ title[num_].toText()
								+ "' data-content='"
								+ text.toText()
								+ "'><span>" + text + "</span></div>";
						} else {
							html = td_l.eq(num_).html();
						}
						td_l.eq(num_).html("");
						td_l.eq(num_).append(html);
					}
				}

				if ($('#' + tableId + " tbody tr").find(".dataTables_empty").length > 0) {
					$('#' + tableId + " tbody tr").find(".pop").popover('destroy');
				} else {
					$('#' + tableId + " tbody tr").find(".pop").popover({
						trigger : 'hover',
						placement : 'top'
					});
				}
			}
		}

		/**
		 * 全选事件
		 */
		var add_event_checkall = function() {
			$("." + containerId + "_checkall").on(
				"change",
				function() {
					$('input[class="' + containerId + '_checkall_item"]').prop(
						"checked", this.checked);
					$("." + containerId + "_checkall").prop("checked",
						this.checked);
				});
		}

		/**
		 * 检测有哪些项选中
		 */
		var is_checkbox_checked = function() {
			var _checked = $('input[class="' + containerId
				+ '_checkall_item"]:checked');
			return _checked.size() > 0;
		}
		/**
		 * 获取选中的checkbox的value
		 */
		var get_checkbox_checked = function() {
			var _checked = $('input[class="' + containerId
				+ '_checkall_item"]:checked');
			var res = new Array();
			if (_checked) {
				_checked.each(function() {
					res.push($(this).val());
				});
			}
			return res;
		}
		/**
		 * 判断是否全选
		 */
		var is_check_all = function(){
			var checkboxs = $('#' + tableId + ' thead input[type="checkbox"]');
			var checkall = null;
			if(checkboxs.length = 1){
				checkall = checkboxs;
			}else if(checkboxs.length > 1){
				console.log("表头中超过两列包含选择框，默认选择第一列");
				checkall = checkboxs[0];
			}else{
				console.log("当前表格未使用选择模式");
				return false;
			}
			if(checkall != null){
				return checkall.prop("checked");
			}
		}

		var getCurrentRequest = function (){
			return $.parseJSON(this.dataTable.ajax.params());
		}

		/**
		 * 搜索控制按钮
		 */
		var add_search_toolbar = function() {
			var search_class = '_search_toolbar_' + containerId;
			var _fo_html_toolbar = '&nbsp;&nbsp;<a href="javascript:;" class="btn btn-primary '
				+ search_class
				+ '"><span><i class="fa fa-search"></i>&nbsp;搜索</span></a>&nbsp;&nbsp;';
			$("div." + containerId + "_toolbar_operate").append(_fo_html_toolbar);
			$("." + search_class).toggle(function() {
				$('#' + tableId + ' thead tr').eq(1).show();
			}, function() {
				$('#' + tableId + ' thead tr').eq(1).hide();
			});
		}
		/**
		 * 添加toolbar
		 */
		var add_top_toolbar = function() {
			for ( var _i in params["toolbar"]) {
				var _fo_html_toolbar = '&nbsp;<a href="javascript:;" class="btn btn-primary '
					+ params["toolbar"][_i]["class"]
					+ '"><span>'
					+ params["toolbar"][_i]["title"] + '</span></a>&nbsp;';
				$("div." + containerId + "_toolbar_operate").append(
					_fo_html_toolbar);
			}
		}
		var add_top_right_toolbar = function() {
			if (undefined != params["toolbarR"]["output"]) {
				var output = params["toolbarR"]["output"];
				var name = output["title"] ? output["title"]
					: defaultParams["toolbarR"]["output"]["title"];
				var _fo_html_toolbar = '&nbsp;<a href="javascript:;" class="btn btn-default output_btn_'
					+ containerId + '"><span>' + name + '</span></a>&nbsp;';
				$("div." + containerId + "_toolbar_operate_right").append(
					_fo_html_toolbar);
			}
		}

		/**
		 * 添加全选事件后的toolbar
		 */
		var add_checkall_toolbar = function() {
			var _fo_html_toolbar = "";
			for ( var _i in params["checkall"]["data"]) {
				_o_d_a = Object.clone(params["checkall"]["data"][_i]);// 需要深复制对象
				_fo_html_toolbar += '&nbsp;<a href="'
					+ (_o_d_a["url"] ? _o_d_a["url"] : "javascript:;")
					+ '" class="DTTT_button '
					+ (_o_d_a["class"] ? _o_d_a["class"] : "") + '"><span>'
					+ (_o_d_a["title"] ? _o_d_a["title"] : "undefined")
					+ '</span></a>';
			}
			$("div." + containerId + "_toolbar_operate").html(_fo_html_toolbar);
		}
		var copy_table_head_to_foot = function() {

		}
		/**
		 * on complete
		 */
		var on_complete_datatables = function() {

			$("#" + containerId).bind('DOMNodeInserted', function(e) {
				if ($(e.target).html().trim() == "1") {
					alert('element now contains: ' + $(e.target).html());
				}
			});
		}

		/**
		 * 判断功能开启
		 */
		var isEnable = function(pa) {
			return params[pa] ? params[pa] : defaultParams[pa];
		}

		var reload = function(op,data) {
			rowSelectedOptions = op;
			params["conditions"] = data;
			datatable.ajax.setParam(ajax_data);
			datatable.ajax.reload(function(){},false);
		}

		var getRowSelectedOptions = function(){
			return rowSelectedOptions;
		}

		var reloadFn = function(op,data,callback,isChangePage) {
			rowSelectedOptions = op;
			params["conditions"] = data;
			datatable.ajax.setParam(ajax_data);
			datatable.ajax.reload(callback,isChangePage);
		}

		var iReload = function(dto, jsonStr) {
			params["ireload"] = [ dto, jsonStr ];
			datatable.ajax.setParam(ajax_data);
			datatable.ajax.reload(function(){},false);
		}

		var iRestrition = function(dto, jsonStr) {
			params["ireload"] = [ dto, jsonStr ];
			datatable.ajax.setParam(ajax_data);
		}

		var iReloadFn = function(dto, jsonStr,callback,isChangePage) {
			params["ireload"] = [ dto, jsonStr ];
			datatable.ajax.setParam(ajax_data);
			datatable.ajax.reload(callback,isChangePage);
		}

		var exportData = function() {
			if (undefined != params["toolbarR"]["output"]) {
				var output = params["toolbarR"]["output"];
				$.ajax(output["url"], {
					type : "POST",
					data : datatable.ajax.params(),
					contentType : "application/json",
					success : function(response) {
						window.location.assign(response.data);
					}
				});
			}
		}

		//show/hide column
		var fnSetColumnVis = function(iCol,flag){
			var oTable=$("#"+tableId).dataTable();
			oTable.fnSetColumnVis(iCol,flag);
		}


		//设置行选中状态
		var setRowSelected = function(op){
			rowSelectedOptions = op?op:rowSelectedOptions;
			op = rowSelectedOptions ;
			var _page_data = datatable.data();
			if(!_page_data || !_page_data.length){
				//没有数据
				rowSelectedOptions = null;
				params["onNoRowSelected"] && params["onNoRowSelected"]();
				return ;
			}
			for(var _s_i in _page_data){
				if(!$.isNumeric(_s_i)){
					continue;
				}
				var _is_all_op_true = false;
				if($.isNumeric(op)){
					if(_page_data[_s_i]['id'] == op){
						_is_all_op_true  = true
					}
				}else{
					_is_all_op_true  = true
					for(var _op_i in op){
						try{
							if(eval("_page_data["+_s_i+"]."+_op_i) != eval("op."+_op_i)){
								_is_all_op_true = false;
							}
						}catch(e){
							console.log(e.message);
						}

					}
				}
				if(_is_all_op_true){
					$("#"+tableId).find("tbody>tr").removeClass("selected");
					$("#"+tableId).find("tbody>tr").eq(_s_i).addClass("selected");
					break;
				}
			}
			//没有可选中的数据
			if(!$("#"+tableId).find("tbody>tr.selected").length){
				rowSelectedOptions = null;
				params["onNoRowSelected"] && params["onNoRowSelected"]();
			}
		}

		//获取行选中数据
		var getRowSelectedData = function(){
			var _tr_obj = $("#"+tableId).find("tbody>tr.selected");
			return datatable.data()[_tr_obj.data("row")];
		}

		var clearRowSelected = function(){
			$("#"+tableId).find("tbody>tr").removeClass("selected");
			rowSelectedOptions = null;
			params["onNoRowSelected"] && params["onNoRowSelected"]();
		}

		var clearQuery = function(){
			var _table_search = $("#"+tableId).find("thead tr.table-search");
			if(_table_search.length){
				_table_search.find("input").each(function(){
					$(this).val("");
					$(this).next("i").removeClass("t-remove").addClass("t-search");
				});
			}
		}

		var execute = function() {
			// on_complete_datatables();

			// datatable 默认参数，理应执行一次
			$.extend($.fn.dataTable.defaults, defaultParams);

			// 创建表主体
			create_table();
			// 一些参数处理
			do_params();

			// datatables
			$("#" + tableId).dataTable(params);

			datatable = $("#" + tableId).DataTable();

			//页面宽度调整
			if(params["scrollX"] || params["scrollY"]){
				datatable.on('column-sizing', function () {
					$("#"+tableId).find(".table-search").hide();
				});
			}
			// checkall
			if (undefined != params["checkall"]) {
				// checkall-toolbar
				add_checkall_toolbar();
				// checkall-event
				add_event_checkall();
			}

			// 搜索
			if (isEnable("isColumnsSearch")) {
				//add_search_input();
				//add_search_toolbar();
			}
			if (undefined != params["toolbar"] && params["toolbar"].length > 0) {
				add_top_toolbar();
			}
			if (undefined != params["toolbarR"]) {
				add_top_right_toolbar();
			}

		}
		execute();

		// 注册方法
		this.isCheckboxChecked = is_checkbox_checked;
		this.getCheckboxChecked = get_checkbox_checked;
		this.isCheckAll = is_check_all;
		this.getCurrentRequest = getCurrentRequest;
		this.dataTable = datatable;
		this.reload = reload;
		this.iReload = iReload;
		this.reloadFn = reloadFn;
		this.iReloadFn = iReloadFn;
		this.tableId = tableId;
		this.exportData = exportData;
		this.containerId = containerId;
		this.fnSetColumnVis=fnSetColumnVis;
		this.setRowSelected = setRowSelected;
		this.getRowSelectedData = getRowSelectedData;
		this.clearRowSelected = clearRowSelected;
		this.getRowSelectedOptions = getRowSelectedOptions;
		this.clearQuery = clearQuery;
	}
	//window.XiaTableV2 = XiaTableV2;
	XiaTableV2.language = [];
// 对象和数组的深拷贝
	Object.clone = function(sObj) {
		if (typeof sObj !== "object") {
			return sObj;
		}
		var s = {};
		if (sObj.constructor == Array) {
			s = [];
		}
		for ( var i in sObj) {
			s[i] = Object.clone(sObj[i]);
		}
		return s;
	}

// 数据时间格式化
	XiaTableV2.dateFormat = function(date, format) {
		if (!date)
			return;
		if (!format)
			format = "yyyy-MM-dd";

		switch (typeof date) {
			case "string":
				//"2015-05-13T16:00:00.000+0000"
				var reg=/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.?\d{0,3}[Z\-+]?(\d{2}:?\d{2})?/;
				var _zone = 0;
				var _zone_minute = 0;
				var _date_date = date.split("\.");
				if(reg.test(date)){
					var reg_Z=/[+|-](\d{0,4})/;
					if(_date_date.length == 2){
						if(_date_date[1].indexOf("+") >=0){
							_zone = _date_date[1].split("+")[1];
						}else if(_date_date[1].indexOf("-") >=0){
							_zone = -_date_date[1].split("-")[1];
						}else if(_date_date[1].indexOf("Z") >=0){
							_zone = _date_date[1].split("Z")[1];
						}
						_zone = parseInt(_zone);
						var _n_date = new Date();
						var _n_zone = reg_Z.exec(_n_date.toString())[1];
						_zone = _zone+parseInt(_n_zone);
						_zone_minute = _zone % 100;
						_zone = _zone / 100;
					}
					_date_date[0] = _date_date[0].replace("T"," ");
				}
				//兼容IE
				_date_date[0] = _date_date[0].replace(/-/g,"/");
				date = new Date(_date_date[0]);
				//时区变化
				date.setHours(_zone+date.getHours());
				date.setMinutes(_zone_minute+date.getMinutes());
				break;
			case "number":
				date = new Date(date);
				break;
		}

		if (!date instanceof Date)
			return;
		var dict = {
			"yyyy" : date.getFullYear(),
			"M" : date.getMonth() + 1,
			"d" : date.getDate(),
			"H" : date.getHours(),
			"m" : date.getMinutes(),
			"s" : date.getSeconds(),
			"MM" : ("" + (date.getMonth() + 101)).substr(1),
			"dd" : ("" + (date.getDate() + 100)).substr(1),
			"HH" : ("" + (date.getHours() + 100)).substr(1),
			"mm" : ("" + (date.getMinutes() + 100)).substr(1),
			"ss" : ("" + (date.getSeconds() + 100)).substr(1)
		};
		return format.replace(/(yyyy|MM?|dd?|HH?|ss?|mm?)/g, function() {
			return dict[arguments[0]];
		});
	}

	window.XiaTableV2= XiaTableV2;

	return XiaTableV2;
});