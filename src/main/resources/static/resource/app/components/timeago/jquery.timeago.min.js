!function(t){"function"==typeof define&&define.amd?define(["jquery"],t):t(jQuery)}(function(t){function i(){var i=e(this),r=n.settings;return isNaN(i.datetime)||(0==r.cutoff||s(i.datetime)<r.cutoff)&&t(this).text(a(i.datetime)),this}function e(i){if(i=t(i),!i.data("timeago")){i.data("timeago",{datetime:n.datetime(i)});var e=t.trim(i.text());n.settings.localeTitle?i.attr("title",i.data("timeago").datetime.toLocaleString()):!(e.length>0)||n.isTime(i)&&i.attr("title")||i.attr("title",e)}return i.data("timeago")}function a(t){return n.inWords(s(t))}function s(t){return(new Date).getTime()-t.getTime()}t.timeago=function(i){return i instanceof Date?a(i):"string"==typeof i?a(t.timeago.parse(i)):"number"==typeof i?a(new Date(i)):a(t.timeago.datetime(i))};var n=t.timeago;t.extend(t.timeago,{settings:{refreshMillis:6e4,allowFuture:!1,localeTitle:!1,cutoff:0,strings:{prefixAgo:null,prefixFromNow:null,suffixAgo:"前",suffixFromNow:"刚刚",seconds:"几秒钟",minute:"约1分钟",minutes:"%d 分钟",hour:"约1小时",hours:"约 %d 小时",day:"1天",days:"%d 天",month:"约1个月",months:"%d 个月",year:"约1年",years:"%d 年",wordSeparator:" ",numbers:[]}},inWords:function(i){function e(e,s){var n=t.isFunction(e)?e(s,i):e,r=a.numbers&&a.numbers[s]||s;return n.replace(/%d/i,r)}var a=this.settings.strings,s=a.prefixAgo,n=a.suffixAgo;this.settings.allowFuture&&0>i&&(s=a.prefixFromNow,n=a.suffixFromNow);var r=Math.abs(i)/1e3,o=r/60,h=o/60,l=h/24,g=l/365,p=45>r&&e(a.seconds,Math.round(r))||90>r&&e(a.minute,1)||45>o&&e(a.minutes,Math.round(o))||90>o&&e(a.hour,1)||24>h&&e(a.hours,Math.round(h))||42>h&&e(a.day,1)||30>l&&e(a.days,Math.round(l))||45>l&&e(a.month,1)||365>l&&e(a.months,Math.round(l/30))||1.5>g&&e(a.year,1)||e(a.years,Math.round(g)),d=a.wordSeparator||"";return void 0===a.wordSeparator&&(d=" "),t.trim([s,p,n].join(d))},parse:function(i){var e=t.trim(i);return e=e.replace(/\.\d+/,""),e=e.replace(/-/,"/").replace(/-/,"/"),e=e.replace(/T/," ").replace(/Z/," UTC"),e=e.replace(/([\+\-]\d\d)\:?(\d\d)/," $1$2"),new Date(e)},datetime:function(i){var e=n.isTime(i)?t(i).attr("datetime"):t(i).attr("title");return n.parse(e)},isTime:function(i){return"time"===t(i).get(0).tagName.toLowerCase()}});var r={init:function(){var e=t.proxy(i,this);e();var a=n.settings;a.refreshMillis>0&&setInterval(e,a.refreshMillis)},update:function(e){t(this).data("timeago",{datetime:n.parse(e)}),i.apply(this)},updateFromDOM:function(){t(this).data("timeago",{datetime:n.parse(n.isTime(this)?t(this).attr("datetime"):t(this).attr("title"))}),i.apply(this)}};t.fn.timeago=function(t,i){var e=t?r[t]:r.init;if(!e)throw new Error("Unknown function name '"+t+"' for timeago");return this.each(function(){e.call(this,i)}),this},document.createElement("abbr"),document.createElement("time")});