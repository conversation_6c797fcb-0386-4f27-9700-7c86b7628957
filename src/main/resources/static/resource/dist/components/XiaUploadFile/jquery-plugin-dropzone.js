/**
 * 依赖
 * jquery.js
 * dropzone.js
 * box.js
 **/
define(['dropzone','bootbox'],function(Dropzone,bootbox){
	var XiaUploadFile = function(options,id){
		var dropzone_id = "dropzone_"+(XiaUploadFile.XiaDropzoneIdIndex++);
		var defaultParams = {
			url:"/common/files/upload/",//文件上传地址
			urlDelete:"/common/files/id/",//文件删除地址
			urlDownload:"/common/files/id/",//下载文件
			urlViewImg:'/common/files/id/{id}/img',
			paramName: "file", // The name that will be used to transfer the file
			maxFilesize: 100, // MB
			fileType:"picture",
			accessToken:"Bearer ",
			accessTokenName:"Authorization",
			dictFileTooBig:XiaI18n("js_dropzone_dictFileTooBig"),
			addRemoveLinks : true,
			dictResponseError: XiaI18n("js_dropzone_dictResponseError"),
			dictDefaultMessage:XiaI18n("js_dropzone_dictDefaultMessage"),
			acceptedFiles:XiaUploadFile.XiaMimeType.picture,
			dictInvalidFileType:XiaI18n("js_dropzone_dictInvalidFileType"),
			maxFiles:10,//上传文件数
			dictMaxFilesExceeded:XiaI18n("js_dropzone_dictMaxFilesExceeded"),
			dictRemoveFile:XiaI18n("js_dropzone_remove"),
			dictRemoveFileConfirm:XiaI18n("confirm_delete"),
			dictRemoveFileConfirmation:XiaI18n("confirm_delete"),
			dictCancelUpload:XiaI18n("js_dropzone_cancel"),
			dictDownloadFile:XiaI18n("js_dropzone_download"),
			dictCancelUploadConfirmation: XiaI18n("js_dropzone_cancelConfirmation"),
			isDelete:true,//判断是否显示删除按钮，true显示,false不显示
			enabled:true,//判断是否允许上传，true允许，false不允许
			isDownload:true,//判断图片或者附件是否允许下载，true允许，false不允许
			//change the previewTemplate to use Bootstrap progress bars
			previewTemplate: "<div class=\"dz-preview dz-image-preview\">\n  <div class=\"dz-details\">\n    <div class=\"dz-filename\"><span data-dz-name></span><div class=\"dz-size\" data-dz-size></div></div>\n    <img data-dz-thumbnail />\n  </div>\n  <div class=\"progress progress-sm progress-striped active\"><div class=\"progress-bar \" data-dz-uploadprogress></div></div>\n  <div class=\"dz-success-mark\"><span></span></div>\n  <div class=\"dz-error-mark\"><span></span></div>\n  <div class=\"dz-error-message\"><span data-dz-errormessage></span></div>\n</div>",
			previewTemplateFile: '<div class="dz-preview dz-file-preview ">\n  <div class=\"dz-file-notice\"><div class=\"dz-success-mark\"><span></span></div>\n  <div class=\"dz-error-mark\"><span></span></div>\n  <div class=\"dz-error-message\"><span data-dz-errormessage></span></div></div><div class="dz-filename">\n<span class="dz-name"><a href="javascript:undefined;" target="_blank" style="cursor: pointer;text-decoration: none;" data-dz-name></a></span><span class="dz-size" data-dz-size></span></div><div class=\"progress progress-sm progress-striped active\"><div class=\"progress-bar \" data-dz-uploadprogress></div></div></div>'
		};
		var settings = jQuery.extend({}, defaultParams, options);
		if(settings.fileType == "picture"){//如果是图片
			settings.isDownload = false;//全部禁止下载
		}
		//上传数量限制
		settings.dictMaxFilesExceeded = settings.dictMaxFilesExceeded.replace(new RegExp("{num}"),settings.maxFiles);
		//文件类型
		if(options['fileType'] && XiaUploadFile.XiaMimeType[options['fileType']]){
			settings.acceptedFiles = XiaUploadFile.XiaMimeType[options['fileType']];
			settings.url += options['fileType'];
		}else{
			//上传路径
			settings.url += "file";
		}

		//文件大小
		settings.dictFileTooBig = settings.dictFileTooBig.replace(new RegExp("{num}"),settings.maxFilesize);

		var uploadHtml = function(){
			var html = "";
			html+='<'+((undefined !=id)?"div":"form")+' class="dropzone" id="'+dropzone_id+'">';
			html+='<div class="fallback">';
			html+='<input  type="file" multiple="" />';
			html+='</div>';
			html+='<div class="dz-message" style="text-align:center; font-size: 14px;color: gray; height: 40px;"><span>'+(settings.enabled?settings.dictDefaultMessage:"<br/><br/>")+'</span></div>';
			html+='</'+((undefined !=id)?"div":"form")+'>';
			return html;
		}
		var myDropzone = null;
		var _prefix = "doc-";

		//普通内嵌模式
		if(undefined !=id){
			$("#"+id).html(uploadHtml());
		}else{
			//上传弹层模式
			bootbox.dialog({
				message: uploadHtml(),
				title: '<i class="fa fa-cloud-download"></i> &nbsp;&nbsp;文件上传',
				width:200,
				height:500,
				buttons: {
					success: {
						label: "确定",
						className: "btn-success",
						callback: function() {
							var queueFiles = myDropzone.getQueuedFiles();
							var uploadingFiles = myDropzone.getUploadingFiles();
							var files = myDropzone.getAcceptedFiles();
							if((queueFiles.length!=0 || uploadingFiles.length!=0) && files.length!=0){
								return false;
							}
							if(options['onComplete']){
								options['onComplete'](files);
							}
							return true;
						}
					}
				}
			});
		}

		var setDocumentsByDocs = function(docs){
			if(docs && docs.length>0){
				for(var i in docs){
					if(!docs[i].deleted){
						myDropzone.addFile({name:docs[i].name,type:docs[i].contentType,size:docs[i].size,server:docs[i]});
						$("#"+dropzone_id+" .dz-message").hide();
					}
				}
			}
		}

		var setDocumentsByTableAndIdAndKey = function(tabel ,id,key){
			$.ajax({
				url:'/common/files/table/'+tabel+'/id/'+id+"/key/"+key,
				type:"get",
				success:function(res){
					if(res.data && res.data.length>0){
						setDocumentsByDocs(res.data);
					}
				}
			});
		}

		try {
			myDropzone = new Dropzone("#"+dropzone_id, settings);

			myDropzone.on("success", function(file,data) {
				$("#"+dropzone_id+" .dz-message").hide();
				setTimeout(function(){
					XiaDropzoneRemoveProcess()
				},1000);
				if(data){
					file.server = data;
				}
			});
			myDropzone.on("removedfile", function(file) {
				if(myDropzone.files.length==0){
					$("#"+dropzone_id+" .dz-message").show();
				}
			});
			//获取
			var getDocuments = function(){
				var files = [];
				if(myDropzone.files.length>0){
					for(var i in myDropzone.files){
						files.push(myDropzone.files[i].server);
					}
				}
				return files;
			}
			//删除
			var removeDocuments = function(isRemoveFromServer){
				if(isRemoveFromServer){
					myDropzone.removeAllFiles();
				}else{
					myDropzone.clearAllFiles();
				}
				$("#"+dropzone_id+" .dz-preview").remove();
				$("#"+dropzone_id+" .dz-message").show();
			}
			//禁止上传
			var disabled = function(){
				myDropzone.disable();
				$("#"+dropzone_id+" .dz-remove").hide();
				$("#"+dropzone_id+" .dz-message span").hide();
			}
			//开启上传
			var enabled = function(){
				myDropzone.enable();
				$("#"+dropzone_id+" .dz-remove").show();
				$("#"+dropzone_id+" .dz-message span").show();
			}
			// myDropzone.setDocumentsByTableAndIdAndKey = setDocumentsByTableAndIdAndKey;
			myDropzone.setDocumentsByDocs=setDocumentsByDocs;
			myDropzone.getDocuments = getDocuments;
			myDropzone.removeDocuments = removeDocuments;
			myDropzone.disabled = disabled;
			myDropzone.enabled = enabled;
			if(settings.enabled){
				myDropzone.enabled();
			}else{
				myDropzone.disabled();
			}

			return myDropzone;
		} catch(e) {
			DEBUG_MSG && DEBUG_MSG("您的浏览器不支持拖拽上传文件","dropzone");
		}

	}
	var XiaDropzoneRemoveProcess = function(){
		$(".dropzone .progress .progress-bar").each(function(){
			var width = $(this).css("width");
			$(this).parent().hide();
		});
	}
	XiaUploadFile.XiaMimeType = {
		//所有图片
		"picture":"image/*",
		"media":"audio/*,video/*",
		//图片，pdf，zip，excel，text，ppt，word,cad,docx,xlsx
		"attachment":"image/*,application/pdf,application/zip,application/octet-stream,application/rar,application/x-zip-compressed,application/xlsx,application/docx,application/ppt,application/xls,application/doc,application/vnd.ms-excel,text/plain,application/vnd.ms-powerpoint,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
		"file":              "image/*,application/pdf,application/zip,application/octet-stream,application/rar,application/x-zip-compressed,application/xlsx,application/docx,application/ppt,application/xls,application/doc,application/vnd.ms-excel,text/plain,application/vnd.ms-powerpoint,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
	}
	XiaUploadFile.XiaDropzoneIdIndex = 1;

//弹层查看图片
	XiaUploadFile.viewPicture = function(path){
		var imgHtml,box,contWidth,img_url;
		img_url = path;
		imgHtml = 	'<div class="cboxLoadedContent" style="text-align:center">'+
			'<div class="img-content"></div>'+
			'<div id="cboxTitle" style="margin-top: 5px;">'+name+'</div>'+
			'<a class="bootbox-close-button" style="  position: absolute;  top: -12px;right: -12px;"><i class="fa fa-times-circle" style="font-size: 22px;color: #fff;"></i></a>'+
			'</div>';

		box = new  bootbox.dialog({
			message:imgHtml,
			title: "",
			buttons: {
				success: {
					className: "btn-success",
					callback: function() {
					}
				}
			}
		});
		//<img class="cboxPhoto" src="'++'" style="cursor: pointer;  float: none;">
		var img = new Image();
		img.src = img_url;
		img.onload = function (){
			//contWidth = 600;//$(this).width();
			if(contWidth>600 ){
				contWidth = 600;
			}
			//img.width = contWidth;
			box.find(".modal-dialog").css("width",contWidth);
			box.find(".img-content").append(img);
		}

		box.find(".modal-header").hide();
		box.find(".modal-footer").hide();
		box.find(".modal-body").css("padding","10px");
		box.find(".modal-content").css("border-radius","5px");
		box.find(".bootbox-close-button").click(function(e){
			bootbox.hideAll()
		});
	}
	window.XiaUploadFile = XiaUploadFile;
})
