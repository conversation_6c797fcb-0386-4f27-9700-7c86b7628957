/**
 * Created by will.song on 2016/10/28.
 */
define(['io','startup','XiaI18n'],function(io,startup){

    //检测当前project
    var refreshCurrentProject = function(){
        if(FO.get_uri_param("current_project") && FO.get_uri_param("current_project")!=localStorage.current_project){
            localStorage.current_project = FO.get_uri_param("current_project");
        }
    }
    refreshCurrentProject();
//entity version
    $.entityVersion = 0;
//更新，创建，删除，成功错误提醒



    /**
     * hash处理menu请求
     */
   /* $(".mymenu a").each(function () {
        var dataUrl = $(this).attr("href");
        var target = $(this).attr("target");
        if (dataUrl != "javascript:;" && target != "_blank") {
            $.hashAjax.register("get", dataUrl, function (data) {
                $("#content").html(data);
                $('#content').slimscroll({ scrollTo : 0 });
            }, "html");
        }
    });*/


    $.hashAjax.register({
        method:'get',
        link: "/chart/dashboard/home",
        type:'html',
        callback:function(data){
            $("#content").html(data);
        }
    });
    $.hashAjax.register({
        method:'get',
        link: "/chart/dashboard/index_new",
        type:'html',
        callback:function(data) {
            $("#content").html(data);
        }
    });
    $.hashAjax.register({
        method:'get',
        link: "/noauth",
        type:'html',
        callback:function(data) {
            $("#content").html(data);
        }
    });


    //$.hashAjax.execute();

    /**
     * 处理菜单点击
     */
    $(".mymenu a").click(
        function (e) {
            // add_active_to_menu($(this).parent());

            var dataUrl = $(this).attr("href");
            var target = $(this).attr("target");
            if (dataUrl != "" && dataUrl != "javascript:;"
                && target != "_blank") {

                sub_menu_click($(this));

                if(FO.newVersionUrls.indexOf(dataUrl)==-1){
                    var newUrl=window.location.pathname.replace('main_v2','main') +
                        window.location.search+"#__aurl="+ encodeURIComponent(dataUrl);
                    window.location.assign(newUrl);
                }
                else{
                    $.hashAjax.request(dataUrl, {});
                    //百度追踪
                }

                //百度追踪
                _hmt && _hmt.push(['_trackPageview', dataUrl]);

                e.preventDefault();
            } else if (dataUrl != "" && dataUrl != "javascript:;"
                && target == "_blank") {
                var openwindow = "";
                if (dataUrl.substr(0, 7) == "http://") {

                    var linked = "&";
                    if (dataUrl.indexOf("?") == -1) {
                        linked = "?";
                    }
                    openwindow = dataUrl + linked;
                } else {
                    var href = location.href.split("#");
                    href = href[0] + "#" + $.hashAjax.__aurl + "="
                        + encodeURIComponent(dataUrl);
                    openwindow = href;
                }
                window.open(openwindow);
                e.preventDefault();
            }

        });

    function sub_menu_click(obj) {
        $(".mymenu li.current").removeClass("current");
        obj.parent().addClass("current");
    }



    /**
     * 处理首次加载时menu 选中状态
     */
    var menu_active = function () {

        var is_add_active_to_menu = function (obj) {
            var hashs = $.hash.getObject();
            var href = obj.find("a").eq(0).attr("href");
            if (hashs && href != "") {
                if (hashs[$.hashAjax.__aurl] && hashs[$.hashAjax.__aurl][0] === "%") {//浏览器兼容
                    href = encodeURIComponent(href);
                }
                if (hashs[$.hashAjax.__aurl] == href) {
                    obj.addClass("current");
                    return true;
                }
            }
            return false;
        }

        var open_menu = function (obj) {
            var p = obj.parent().parent();
            if (p.hasClass("has-sub-sub") || p.hasClass("has-sub")) {
                jQuery('> a > span.arrow', p).addClass("open");
                p.addClass("open");
                jQuery('>ul', p).show();
            }
        }
        //主页地址
        var _home_a = $(".mymenu i.s-menu-home").parent();
        if(isHome()){
            _home_a.attr("href","/chart/dashboard/home");
        }else{
            _home_a.attr("href","/chart/dashboard/index_new");
        }

        //先清理
        $(".mymenu li.current").removeClass("current");
        var hashs = $.hash.getObject();
        if (null != hashs) {
            var isExist = false;
            var _a1 = $(".mymenu >li");
            for (var i1 = 0; i1 < _a1.length; i1++) {
                var m1 = $(_a1[i1]);
                var isBreak = false;
                if (m1.hasClass("has-sub")) {
                    var _a2 = m1.find(" ul.sub > li");
                    for (var i2 = 0; i2 < _a2.length; i2++) {
                        var m2 = $(_a2[i2]);
                        if (m2.hasClass("has-sub-sub")) {
                            var _a3 = m2.find(" ul.sub-sub > li");
                            for (var i3 = 0; i3 < _a3.length; i3++) {
                                var m3 = $(_a3[i3]);
                                if (is_add_active_to_menu(m3)) {
                                    isBreak = true;
                                    open_menu(m3);
                                    break;
                                }
                            }
                        } else {
                            if (is_add_active_to_menu(m2)) {
                                isBreak = true;
                            }
                        }
                        if (isBreak) {
                            open_menu(m2);
                            break;
                        }
                    }
                } else {
                    if (is_add_active_to_menu(m1)) {
                        isBreak = true;
                    }
                }
                if (isBreak) {
                    open_menu(m1);
                    m1.parent().show();
                    isExist = true;
                    break;
                }
            }
            if(!isExist){
                if(userName && userName == 'f1auadmin'){
                    //判断是否拥有授权权限
                    var _auth_a = $(".mymenu i.s-menu-system").parent();
                    if((!_home_a || _home_a.length == 0) && _auth_a[0]){
                        _auth_a[0].click();
                    }
                }else {
                    //打开主页
                    _home_a.click();
                }
            }
        }else{
            if(userName && userName == 'f1auadmin'){
                //判断是否拥有授权权限
                var _auth_a = $(".mymenu i.s-menu-system").parent();
                if((!_home_a || _home_a.length == 0) && _auth_a[0]){
                    _auth_a[0].click();
                }
            }else {
                //打开主页
                _home_a.click();
            }
        }
    }

    //menu_active();


    /**
     * 打开Menu页面
     */
    window.openMenuPage = function (url, params) {
        var isInPage = false;
        $(".mymenu a").each(function(){
            if($(this).attr("href") == url){
                isInPage = true;
                return;
            }
        });
        if(!isInPage){
            bootbox.alert("<span style='text-align:center;display: block;'>"+XiaI18n("ajax_no_permission")+"</span>");
            return ;
        }
        $.hashAjax.request(url, params);

        //百度追踪
        _hmt && _hmt.push(['_trackPageview', url]);

        //是否在当前页面
        var hash = $.hash.getObject();
        var aurl = hash["__aurl"];
        if(aurl && aurl[0]==="/"){//浏览器兼容
            aurl = encodeURIComponent(aurl);
        }
        if(url && url[0]==="/"){//浏览器兼容
            url = encodeURIComponent(url);
        }
        if(aurl!=url){
            closeAllMenu();
            menu_active();
        }
    }
    /**
     * 新开页面
     */
    window.openMenuBlankPage = function (url,params) {
        if(url && url[0]==="/"){//浏览器兼容
            url = encodeURIComponent(url);
        }
        var href = location.href.split("#");
        href = href[0] + "#" + $.hashAjax.__aurl + "=" + url+"&"+params;

        //百度追踪
        _hmt && _hmt.push(['_trackPageview', href]);

        var a = $("<a href='"+href+"' target='_blank'>Apple</a>").get(0);
        var e = document.createEvent('MouseEvents');
        e.initEvent( 'click', true, true );
        a.dispatchEvent(e);
    }

    window.openNewBlankPage = function (url,query) {
        var currentproject = getCurrentProject();

        if (url.indexOf("http") == -1) {
            url = location.protocol + "//" + location.host + PUBLIC_PATH + url;

            if (query == null) {
                url = url + "?current_project=" + currentproject;
            } else {
                url = url + "?current_project=" + currentproject + "&" + query;
            }
        }

        //百度追踪
        _hmt && _hmt.push(['_trackPageview', url]);

        var a = $("<a href='"+url+"' target='_blank'>Apple</a>").get(0);
        var e = document.createEvent('MouseEvents');
        e.initEvent( 'click', true, true );
        a.dispatchEvent(e);
    }

    window.setTempParameter=function(data){
        if(localStorage){
            localStorage.tempParameter = data;
        }else{
            window.tempParameter = data;
        }
    }

    window.getTempParameter=function(){
        var parameter = undefined;
        if(localStorage){
            parameter = localStorage.tempParameter;
        }else{
            parameter = window.tempParameter;
        }
        localStorage.tempParameter = undefined;
        window.tempParameter = undefined;
        return parameter;
    }

    window.closeAllMenu = function () {

        var last = jQuery('.has-sub-sub.open', $('.sidebar-menu'));
        last.removeClass("open");
        jQuery('.arrow', last).removeClass("open");
        jQuery('.sub-sub', last).slideUp(200);
        jQuery('.current', last).removeClass("current");

        last = jQuery('.has-sub.open', $('.sidebar-menu'));
        last.removeClass("open");
        jQuery('.arrow', last).removeClass("open");
        jQuery('.sub', last).slideUp(200);
        jQuery('.current', last).removeClass("current");

        $("ul.mymenu").hide();
    }

    /** 处理面包屑 */
    window.setPageHeader = function (desc, data) {
        var _data = null;
        var last = null;
        var currentproject = getCurrentProject();

        var homeUri = location.protocol+"//"+location.host + location.pathname + "?current_project="+currentproject;

        // _data["主页"] = "/";
        _data = new Object();
        var mymenu = $(".mymenu");
        var p0 = mymenu.find("li.current");
        if (p0.length != 0) {
            var p1 = p0.parent().parent("li");
            if (p1.length != 0) {
                var p2 = p1.parent().parent("li");
                if (p2.length != 0) {
                    _data[p2.find(">a").text()] = p2.find(">a").attr("href");
                }
                _data[p1.find(">a").text()] = p1.find(">a").attr("href");
            }
            _data[p0.find(">a").text()] = p0.find(">a").attr("href");
        }

        var html = "";
        for (var name in _data) {
            if (name) {
                html += '<li><a href="' + _data[name] + '">' + name + '</a></li>';
                last = name;
            }
        }
        if (data) {
            for (var name in data) {
                html += '<li><a href="' + data[name] + '">' + name + '</a></li>';
                last = name;
            }
        }

        html = '<div class="page-header" style="min-height: 0;"><h3 style="margin: 0px;">' + last + '</h3><ul class="breadcrumb"><li><i class="fa fa-home"></i><a href="' + homeUri + '"> ' + $("#page-header").attr("data-home") + '</a></li>' + html + '</ul></div>';

        // $(html).prependTo();
        $("#page-header").html(html);

        $(".breadcrumb a").click(
            function (e) {
                var dataUrl = $(this).attr("href");
                var target = $(this).attr("target");
                if (dataUrl != "javascript:;" && target != "_blank"
                    && dataUrl != homeUri) {

                    $.hashAjax.request(dataUrl, {});
                    e.preventDefault();
                }
            });
    }

    /**
     * 截取中文
     */
    var mycutstr = function (str, len) {
        if (!str) {
            return "";
        }

        var str_length = 0;
        var str_len = 0;
        str_cut = new String();
        str_len = str.length;
        for (var i = 0; i < str_len; i++) {
            a = str.charAt(i);
            str_length++;
            if (escape(a).length > 4) {
                //中文字符的长度经编码之后大于4
                str_length++;
            }
            str_cut = str_cut.concat(a);
            if (str_length >= len) {
                str_cut = str_cut.concat("...");
                return str_cut;
            }
        }
        //如果给定字符串小于指定长度，则返回源字符串；
        if (str_length < len) {
            return str;
        }
    }

    /**
     * 消息提醒
     */
    $(function () {

        // 默认图片
        var imgInfo = PUBLIC_PATH+"/resource/js/js-plugin/gritter/images/info.png";
        var imgError = PUBLIC_PATH+"/resource/js/js-plugin/gritter/images/error.png";
        var imgWarning = PUBLIC_PATH+"/resource/js/js-plugin/gritter/images/warning.png";

        if(typeof  _messagesite_port == 'undefined'){
            return;
        }

        var connectPach = location.protocol + "//" + location.hostname + ":" + _messagesite_port;
        if(location.port == 0 || 2 == location.port.length){
            connectPach = location.protocol + "//" + location.hostname;
        }
        // socket链接成功
        var socket = io.connect(connectPach);
        //var socket = io.connect(connectPach,{"transports":["websocket"]});
        //var socket = io.connect(connectPach,{"transports":["polling"],"forceJSONP":true});
        socket.on('connect', function () {
            var userId = $("#user_id").val();
            var userName = $("#user_name").val();

            console.log(startup);

            /*var gritter_id = $.gritter.add({
             title: "welcome",
             text: "Hello " + userName,
             image: imgInfo,
             sticky: false,
             time: "3000",
             // position:data.position,
             class_name: "gritter-light"
             });*/

            $("#gritter-notice-wrapper").css("top","60px");

            //发送一个请求，
            var jsonObject = {userId: userId};
            socket.emit(_messagesite_channel, jsonObject);
        });

        // 监听
        socket.on(_messagesite_channel, function (data) {
            //延迟查询
            setTimeout(function () {
                document.getElementById("noticeMp3").play();
                startup.header && startup.header.getNotifications();

                if ((getCurrentProject() === 0 || getCurrentProject() === '0')
                    || (data.site && (data.site.project==getCurrentProject()))) {
                    startup.header.$notify({
                        title: data.title,
                        message: data.content,
                        type: 'warning',
                        duration:3000
                    });
                }
            }, 5000);
            return

            // // +1
            // var number = $("#header-notification .badge").text();
            // number = parseInt(number);
            // number = number ? number : 0;
            // number = number + 1;
            // $("#header-notification .badge").text(number);
            //
            // // 判断图片
            // var imgSrc = imgInfo;
            // if (data.image) {
            //     imgSrc = data.image
            // }
            // if (data.type == "info") {
            //     imgSrc = imgInfo;
            // } else if (data.type == "error") {
            //     imgSrc = imgError;
            // } else if (data.type == "warning") {
            //     imgSrc = imgWarning;
            // }
            // if (!data.sticky) {
            //     data.sticky = false;
            // }
            // if (!data.showTime) {
            //     data.showTime = 3000;
            // }
            // if (!data.skin) {
            //     data.skin = "gritter-light";
            // }
            // // 弹出消息
            // var gritter_id = $.gritter.add({
            //     title: data.title,
            //     text: data.content,
            //     image: imgSrc,
            //     sticky: data.sticky,
            //     time: data.showTime,
            //     //position : data.position,
            //     class_name: data.skin
            // });
            // //播放声音
            // document.getElementById("noticeMp3").play();
            //
            // if (data.site) {
            //     buildMessage(data.site, false);
            // }
        });


        var buildMessage = function (m, isInit) {
            // 头像
            var tpl = '<li>';
            tpl += '<a>';
            tpl += '<img alt="" src="' + user_default_head_pic + '">';
            tpl += '<span class="body">';
            tpl += '<span style="display:block;width:220px;">';
            tpl += '<span class="from">' + (m.sender?m.sender:"&nbsp;") + '</span>';
            tpl += '<span class="time">';
            tpl += '<i class="fa fa-clock-o"></i>';
            tpl += '<span style="margin-left: 5px;"><abbr class="timeago" title="' + new Date(m.createdDate).format('yyyy-MM-dd HH:ss:mm') + '"></abbr></span>';
            tpl += '</span>';
            tpl += '</span>';
            tpl += '<span class="message msg-site"><p>' + mycutstr(m.content, 55) + '</p></span>';
            tpl += '</span>';
            tpl += '</a>';
            tpl += '</li>';
            var obj = $(tpl);
            obj.click(function () {
                showMeaageSite();
            });
            obj.appendTo($("#unreadmessage"));
            obj.find("abbr.timeago").timeago();

            if (!isInit) {
                var number = $(".unreadnumber").eq(0).text();
                number = parseInt(number);
                number = number + 1;
                $(".unreadnumber").text(number);
            }
        }

        /*$.ajax({
            url:"/uc001/messages/unread/3",
            type:"get",
            success:function(response){
                if(response.data){
                    if(response.data.total){
                        $(".unreadnumber").text(response.data.total);
                        $(".unreadnumber").show();
                        for(var m in response.data.list){
                            buildMessage(response.data.list[m],true);
                        }
                    }else{
                        $(".unreadnumber").eq(0).hide();
                    }
                }
            }
        });*/

    });

    /**
     * notice
     */
        /*
    var BootStrapNotice = {
        error: function (message) {
            var tmpl = {
                color: "alert-danger",
                icon: "fa-times-circle"
            };
            BootStrapNotice._show(tmpl, message);
        },
        info: function (message) {
            var tmpl = {
                color: "alert-info",
                icon: "fa-info-circle"
            };
            BootStrapNotice._show(tmpl, message);
            BootStrapNotice._closeAutoEvent();
        },
        success: function (message) {
            var tmpl = {
                color: "alert-success",
                icon: "fa-check-circle"
            };
            BootStrapNotice._show(tmpl, message);
            BootStrapNotice._closeAutoEvent();
        },
        warning: function (message) {
            var tmpl = {
                color: "alert-warning",
                icon: "fa-exclamation-triangle"
            };
            BootStrapNotice._show(tmpl, message);
        },
        _html: function (tmpl, message) {
            BootStrapNotice._id++;
            var html = '<div id="'
                + BootStrapNotice._getId()
                + '" class="alert alert-block '
                + tmpl.color
                + ' fade in" style="margin-bottom: 0;position:relative;">';
            html += '<a class="close" data-dismiss="alert" href="#" aria-hidden="true">×</a>';
            html += '<h1 style="position: absolute;top: -20px;"><i class="fa '
                + tmpl.icon + '"></i></h1>';
            html += '<p style="font-size:16px;padding-left: 40px;">' + message
                + '</p></div>';
            return html;
        },
        _show: function (tmpl, message) {
            var htmlContent = BootStrapNotice._html(tmpl, message);
            htmlContent = $(htmlContent);
            $("#index_notice").append(htmlContent);
            BootStrapNotice._closeEvent(htmlContent);
            $("#index_notice").width($("#main-content").width()-20);
            return htmlContent;
        },
        _closeEvent: function (obj) {
            obj.find("a.close").click(function () {
                obj.remove('slow');
            });
        },
        _closeAutoEvent: function () {
            var id = BootStrapNotice._getId();
            setTimeout("XiaBootStrapNotice._hide(" + id + ")", 3000);
        },
        _hide: function (id) {
            $(id).hide("slow");
            setTimeout("XiaBootStrapNotice._remove(" + $(id).attr("id") + ")", 1000);
        },
        _remove: function (id) {
            $(id).remove();
        },
        _id: 0,
        _getId: function () {
            return "index_notice_" + BootStrapNotice._id;
        }

    }
    window.XiaBootStrapNotice = window.BootStrapNotice = BootStrapNotice;
*/
    /**
     * 获取Token
     */
    window.getAccessToken = function () {
        return "";
    }

    window.getCurrentProject = function () {
        var finalCurrentProject = null;
        var currentproject = localStorage.current_project;
        var currentprojectFromUrl = FO.get_uri_param("current_project");
        if(!currentproject&&currentprojectFromUrl){
            finalCurrentProject = currentprojectFromUrl;
        }else if(!currentprojectFromUrl&&currentproject){
            currentprojectFromUrl = 0;
            finalCurrentProject = currentprojectFromUrl;
        }else{
            if(currentproject!=currentprojectFromUrl){
                finalCurrentProject = currentprojectFromUrl;
            }else{
                if (currentprojectFromUrl == undefined) {
                    currentprojectFromUrl = 0;
                }
                if (currentproject == undefined) {
                    currentproject = 0;
                }
                finalCurrentProject = currentproject;
            }
        }
        return finalCurrentProject;
    }


    var doFixButtonGroup = function (cls) {
        var lastScrollTop;
        $(window).resize(function () {
            var width = 0;
            if ($(window).width() > 768) {
                width = jQuery(window).width() - jQuery(sidebar).width() - 15;
            } else {
                width = jQuery(window).width() - 15;
            }
            //if ($(window).width() > 1215) {
            //    jQuery(cls).css("top", "0px");
            //} else {
            //    jQuery(cls).css("top", "-45px");
            //}

            jQuery(cls).css("width", width + "px");
            jQuery(cls).css("padding-right", "15px");
        });

        jQuery(window).bind("scroll", function () {
            var width = 0;
            if ($(window).width() > 768) {
                width = jQuery(window).width() - jQuery(sidebar).width() - 15;
            } else {
                width = jQuery(window).width() - 15;
            }
            //if ($(window).width() > 1215) {
            //    jQuery(cls).css("top", "0px");
            //} else {
            //    jQuery(cls).css("top", "-45px");
            //}

            jQuery(cls).css("width", width + "px");
            jQuery(cls).css("padding-right", "15px");

            var t = jQuery(window).scrollTop();

            var top = 0;
            var contentOffeset = 0;
            if ($(window).width() > 1215) {
                contentOffeset = jQuery("#content").offset().top;
                top = 0;
            } else {
                contentOffeset = jQuery("#content").offset().top - 45;
                top = -50;
            }

            // down
            if (lastScrollTop < t && t > contentOffeset) {
                jQuery(cls).css("position", "fixed");
                jQuery(cls).each(function (index, element) {
                    jQuery(element).stop().animate({top: "0"}, 300);
                })
            } else if (lastScrollTop < t && t < contentOffeset) {
                jQuery(cls).css("position", "relative");
            }

            // up
            if (lastScrollTop > t && t > contentOffeset) {
                jQuery(cls).css("position", "fixed");
                jQuery(cls).each(function (index, element) {
                    jQuery(element).stop().animate({top: "0"}, 300);
                })
            } else if (lastScrollTop > t && t < contentOffeset) {
                jQuery(cls).css("position", "relative");
            }
            lastScrollTop = t;
        })
    }

    /**
     * 首页 header project
     */
    $(function(){
        //projects
        $("#header-projects .dropdown-menu").find("a").click(function(){

            var url = location.protocol+"//"+location.host;
            if($(this).data("pid")){
                localStorage.current_project = $(this).data("pid");
                url += "/main/home/"+$(this).data("pid")+"?current_project="+$(this).data("pid");

                //百度追踪
                _hmt && _hmt.push(['_trackPageview', "/main/home/" + $(this).data("pid") + "?current_project=" + $(this).data("pid")]);

            }else{
                localStorage.current_project = 0;
                url += "/main_v2/index#__aurl=%2Fchart%2Fdashboard%2Findex_new";

                //百度追踪
                _hmt && _hmt.push(['_trackPageview', "/main_v2/index"]);
            }
            location.href=url;
        });
    });

   // App.init();
    return { header: startup.header, menu:startup.menu }
})