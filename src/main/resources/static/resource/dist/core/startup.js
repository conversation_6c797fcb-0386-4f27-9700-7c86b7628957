/**
 * Created by will.song on 2016/10/28.
 */
define(['vue',
        'jquery',
        'ELEMENT',
        'FMOne',
        'core.notice',
        'core.messagebox',
        'app.header',
        'app.menu',
        'common',
        'ELEMENT.locale.zh-cn',
        'ELEMENT.locale.en',
        'ELEMENT.locale.zh-hk',
        'jquery.cookie',
        'jquery.hash'],
    function(Vue, $, ELEMENT, FMOne, Notice, MessageBox, header, menu, common, cn, en,hk ){
        Vue.config.errorHandler = function (err, vm) {
            console.log(err);
        }

        Vue.filter("date", function(value, format) {   //全局方法 Vue.filter() 注册一个自定义过滤器,必须放在Vue实例化前面
            return common.dateFormat(value, format);
        });
        
        var jquery_ajax_complete_error = function(XMLHttpRequest, textStatus){
            // 401 token过期重新请求
            if (XMLHttpRequest.status == "401" || XMLHttpRequest.status == "406") {
                MessageBox.alert({
                    message: XiaI18n("not_operate_long_time"),
                    callback: function () {
                        if (coeEnabled == "true") {
                            $.get("/logout", function (data) {
                                //百度追踪
                                _hmt && _hmt.push(['_trackPageview', coeUrl]);

                                window.location.href = coeUrl;
                            });
                        } else {
                            window.location.reload();
                        }
                    }
                });
            } else  if (XMLHttpRequest.status == "403") {
                MessageBox.alert({ message: XiaI18n("ajax_no_permission") });
            } else if (XMLHttpRequest.status == "200") {
                location.reload();
            } else {
                try {
                    var Result = $.parseJSON(XMLHttpRequest.responseText);
                } catch (e) {
                    DEBUG_MSG && DEBUG_MSG(XMLHttpRequest.responseText, "ajax-parse-response-error");
                }
                if (Result.code == "311") {//ValidationException
                    if (Result.data) {
                        var errorMsgBuilder = function (name, msg) {
                            return '<small class="help-block s-form-valid-error"  style="display: block;">' + msg + '</small>';
                        }
                        for (var d in Result.data) {
                            var input = $(".form-group input[name='" + d + "']");
                            if (!input.length) {
                                input = $(".form-group input[valid-name='" + d + "']");
                            }
                            if (input.length) {
                                var msg = errorMsgBuilder(input.attr("name"), Result.data[d]);
                                input.parent().parent(".form-group").addClass("has-error");
                                input.parent().parent(".form-group").removeClass("has-success");
                                input.parent().find(".s-form-valid-error").remove();
                                input.keyup(function () {
                                    input.parent().find(".s-form-valid-error").remove();
                                });
                                $(msg).insertAfter(input);
                            }
                        }
                    }
                } else if (Result.code == "312") {//OptimisticLockException
                    var obj = this;
                    if (obj.versionCallback) {
                        bootbox.alert(XiaI18n("ajax_data_version_old"), function () {
                            eval(obj.versionCallback);
                        });
                    }
                }else if (Result.code == "310" || Result.code == "313") {
                    var msg =Result.message.split("\n")[0];
                    Notice.error(msg)
                }else if (Result.message) {
                    DEBUG_MSG && DEBUG_MSG(Result.message, "ajax-error");
                }
            }
        }

        $.ajaxSetup({
            contentType: "application/json",
            dataType: "json",
            processData: false,
            cache: false,//关闭浏览器get请求的缓存（IE）
            complete: function (XMLHttpRequest, textStatus) {
                if ("success" == textStatus) {
                    try {
                        var Result = $.parseJSON(XMLHttpRequest.responseText);
                        if (undefined != Result.code && Result.code == "200") {// Result.class格式数据
                            if (Result.status == "success") {
                                Result.message && Notice.success(Result.message);
                            } else if (Result.status == "fail") {
                                Result.message && Notice.warning(Result.message)
                            } else if (Result.status == "error") {
                                Result.message && Notice.error(Result.message);
                            }
                        } else if (Result.message) {
                            Result.message && Notice.error(Result.message);
                        }

                    } catch (e) {
                        ;//不做处理
                    }
                } else{//异常情况
                    jquery_ajax_complete_error(XMLHttpRequest, textStatus);
                }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                //jquery_ajax_complete_error(XMLHttpRequest, textStatus);
            },
            beforeSend: function (xhr) {
                var finalCurrentProject = null;
                var currentproject = localStorage.current_project;
                var currentprojectFromUrl = FO.get_uri_param("current_project");
                if(!currentproject&&currentprojectFromUrl){
                    finalCurrentProject = currentprojectFromUrl;
                }else if(!currentprojectFromUrl&&currentproject){
                    finalCurrentProject = currentproject;
                }else{
                    if(currentproject!=currentprojectFromUrl){
                        finalCurrentProject = currentprojectFromUrl;
                    }else{
                        finalCurrentProject = currentproject;
                    }
                }
                var tempProject = finalCurrentProject;
                var currentproject = tempProject?tempProject:0;
                xhr.setRequestHeader('CurrentProject', currentproject);
            }
        });

        $.hashAjax.init();

        var lang = { 'zh_CN': cn, 'en_US': en,'zh_HK':hk };
        var locale = document.documentElement.lang ? lang[document.documentElement.lang] : lang['zh_CN'];

        Vue.use(ELEMENT, { locale: locale });
        Vue.use(FMOne);

        var headerVM = header.install(Vue, $);
        var menuVM = menu.install(Vue, $);

        //menuVM.selectMenu = $.hashAjax.getHashValue().path;

        headerVM.$on('open-menu', function(url){
            var hashValue = $.hashAjax.option.get(encodeURIComponent((url)));
            if(hashValue){
                menuVM.openMenu(hashValue.path);
                //menuVM.selectMenu = hashValue.path;
            }
            else{
                MessageBox.alert({ message: XiaI18n("ajax_no_permission") });
            }
        });

        headerVM.$on('narrow-changed', function(isNarrow){
           menuVM.isNarrow = isNarrow;
        });

        return {header:headerVM, menu: menuVM};
});
