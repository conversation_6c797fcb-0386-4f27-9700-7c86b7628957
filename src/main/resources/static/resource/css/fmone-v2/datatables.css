.fmone-v2 table.dataTable thead th,
.fmone-v2 table.dataTable thead td {
    padding: 14px 10px;
    border-bottom: 1px solid #ddd;
    border-right: 1px solid #dfdfdf;
}

.fmone-v2 table.dataTable thead tr:last-child td {
    padding: 5px;
}

.fmone-v2 .dataTables_wrapper .dataTables_info {
    clear: both;
    float: left;
    padding-top: 1.6em;
}

.fmone-v2 .dataTables_wrapper .dataTables_paginate {
    float: right;
    text-align: right;
    padding-top: 1.25em;
    padding-bottom: 0.25em;
    padding-right: 2px;
}

.fmone-v2 .dataTables_wrapper .dataTables_paginate .paginate_button {
    box-sizing: border-box;
    display: inline-block;
    min-width: 26px;
    padding: 2px 9px;
    margin-left: -1px;
    text-align: center;
    text-decoration: none !important;
    cursor: pointer;
    *cursor: hand;
    color: #666666 !important;
    border: 1px solid #dddddd;
    background-color: #ffffff;
}

.fmone-v2 .dataTables_wrapper .dataTables_paginate .paginate_button.current,
.fmone-v2 .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    color: #ffffff !important;
    border: 1px solid #1ab394;
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,  #1ab394), color-stop(100%,  #1ab394));
    /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #1ab394 0%,  #1ab394 100%);
    /* Chrome10+,Safari5.1+ */
    background: -moz-linear-gradient(top,  #1ab394 0%,  #1ab394 100%);
    /* FF3.6+ */
    background: -ms-linear-gradient(top,  #1ab394 0%,  #1ab394 100%);
    /* IE10+ */
    background: -o-linear-gradient(top,  #1ab394 0%,  #1ab394 100%);
    /* Opera 11.10+ */
    background: linear-gradient(to bottom,  #1ab394 0%,  #1ab394 100%);
    /* W3C */
    background-color: #1ab394;
}

.fmone-v2 .sidebar-menu > ul > li.has-sub .sub {
    position: relative;
    background: #22262e;
    border-top: 0;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1) inset;
}

.fmone-v2 .sidebar-menu > ul > li > ul.sub > li,
.fmone-v2 .sidebar-menu > ul > li > ul.sub .has-sub-sub ul.sub-sub > li {
    background: none;
    margin: 0px;
    padding: 0px;
    margin-top: 0px!important;
}
.fmone-v2 .dataTables_wrapper .dataTables_paginate .paginate_button:active{
    box-shadow: inset 0 0 3px #eee;
}

.bootbox-body .dataTables_wrapper .dataTables_paginate .paginate_button:active{
    box-shadow: inset 0 0 3px #eee;
}

.fmone-v2 table.dataTable tbody tr{
    background-color: #F3F4F4;
}

/* datatables */
.fmone-v2 .dataTables_wrapper {
    padding: 0;
}

.fmone-v2 .dt_op_a{
    color: #1c83c6;
    margin-left:14px;
}

.fmone-v2 .dt_op_a:first-child{
    margin-left:0;
}

.fmone-v2 table.dataTable.display tbody td.center{text-align: left}

.fmone-v2 table.dataTable.display tr.empty-data{
    text-align: center;
}

.fmone-v2 table.dataTable.display tr.empty-data td{ padding:10px }

.container .fmone-v2 table.dataTable .form-control::-webkit-input-placeholder {color: #b2b2b2;  }
.container .fmone-v2 table.dataTable .form-control:-moz-placeholder{color: #b2b2b2;  }
.container .fmone-v2 table.dataTable .form-control::-moz-placeholder{color: #b2b2b2;  }
.container .fmone-v2 table.dataTable .form-control:-ms-input-placeholder{color: #b2b2b2;  }

.container .fmone-v2 table.dataTable .table-search input::-webkit-input-placeholder{color: #b2b2b2;}
.container .fmone-v2 table.dataTable .table-search input:-moz-placeholder{color: #b2b2b2;}
.container .fmone-v2 table.dataTable .table-search input::-moz-placeholder{color: #b2b2b2;}
.container .fmone-v2 table.dataTable .table-search input:-ms-input-placeholder{color: #b2b2b2;}


.bootbox.fmone-v2 table.dataTable .form-control{min-height: 0;height: 28px;margin: -2px 0;}
.container .fmone-v2 table.dataTable .form-control{ height: 28px; min-height:28px; border: 1px solid #e7e7e7;padding: 0px 6px;}
.container .fmone-v2 table.dataTable .form-control.ms-ctn-focus{border-color: #1ab394;}
.container .fmone-v2 table.dataTable .ms-ctn .ms-sel-ctn{height: 28px;margin-left:0; overflow: hidden}
.container .fmone-v2 table.dataTable .ms-ctn input{margin:0;}
.container .fmone-v2 table.dataTable .ms-ctn {padding-top:3px; padding-right: 0;}
.container .fmone-v2 table.dataTable .ms-ctn .ms-sel-item{padding: 1px 4px;margin: 4px 6px 4px 0;}

.container .fmone-v2 table.vertical-align-top tr.odd,
.container .fmone-v2 table.vertical-align-top tr.even{
    vertical-align: top;
}

.fmone-v2 .ms-ctn .ms-sel-ctn{ margin-left:0}

.fmone-v2 .ms-ctn .ms-trigger,
.container .fmone-v2 .ms-ctn .ms-trigger{border-left: 1px solid #eee;}

.fmone-v2 .ms-ctn .ms-trigger,
.container .fmone-v2 .ms-ctn .ms-trigger:hover{background-color: transparent}

.container .fmone-v2 table.dataTable .xia-input-clear i.fa{right: 16px;top: 11px;}

.fmone-v2 .ms-inv {
    border-color: #f56954;
}
.fmone-v2 .ms-ctn-focus {
    border-color: #1ab394;
}

.fmone-v2 .dataTables_wrapper {min-height:80px;}


.fmone-v2 .dataTables_wrapper .dataTables_paginate .paginate_button:hover{
    border: 1px solid #1ab394;
    background: #1ab394;
    color: #1ab394 !important;
}

.bootbox.fmone-v2 .dataTables_wrapper .dataTables_processing,
#content .fmone-v2 .dataTables_wrapper .dataTables_processing{
    z-index: 100;
    top: 0;
    position: absolute;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,.6);
    color: #fff;
    width: 200px;
    border-radius: 8px;
    left: 0;
    margin: auto;
    height: 60px;
    padding: 0;
    line-height: 60px;
}

.bootbox.fmone-v2 .dataTables_wrapper .dataTables_processing i,
#content .fmone-v2 .dataTables_wrapper .dataTables_processing i{
    display:block;
}

.bootbox.fmone-v2 .dataTables_wrapper .dataTables_processing img,
#content .fmone-v2 .dataTables_wrapper .dataTables_processing .processing-content img{
    display:none;
}

.fmone-v2 .processing-content .fa-refresh{
    -webkit-animation: loadingCircle 1s infinite linear;
    animation: loadingCircle 1s infinite linear;
    height: 14px;
    font-weight: 100;
    font-size: 14px;
}

.fmone-v2 .dataTables_wrapper .table-search select{
    margin: -4px 0;
    padding: 3px;
}

.fmone-v2 .table_toolbar_operate { display: none}

@-webkit-keyframes loadingCircle {
    0% {
        -webkit-transform-origin: 50% 50%;
        transform-origin: 50% 50%;
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform-origin: 50% 50%;
        transform-origin: 50% 50%;
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
@keyframes loadingCircle {
    0% {
        -webkit-transform-origin: 50% 50%;
        transform-origin: 50% 50%;
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform-origin: 50% 50%;
        transform-origin: 50% 50%;
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

.fmone-v2 table.dataTable thead .sorting,
.fmone-v2 table.dataTable thead .sorting_asc,
.fmone-v2 table.dataTable thead .sorting_desc{
    background-position-y: 1px;
}