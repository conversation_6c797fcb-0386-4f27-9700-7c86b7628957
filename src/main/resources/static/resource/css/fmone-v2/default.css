.fmone-v2 .button-bar,
.fmone-v2 .s-button-tools {
    width:auto;
    padding: 14px 0;
    z-index: 1000;
    background-color: #fff;
    margin-left: 15px;
    margin-right: 15px;
    margin-top: 10px;
    border-bottom:1px solid #eee;
}

#sidebar.fmone-v2  {
    background: none repeat scroll 0 0 #2f4050;
    padding: 0 !important;
    width: 220px;
    position: absolute;
    top: 50px;
    /* border-bottom: 1px solid #39435C;*/
}

.fmone-v2 .sidebar-menu > ul > li > a {
    display: block;
    position: relative;
    margin: 0;
    border: 0 none;
    padding: 16px 15px 15px 10px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 400;
    text-transform: uppercase;
}

.fmone-v2 .sidebar-menu > ul > li.has-sub .sub {
    position: relative;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1) inset;
}

.fmone-v2 .sidebar-menu > ul > li > ul.sub:before {
    -moz-border-bottom-colors: none;
    -moz-border-left-colors: none;
    -moz-border-right-colors: none;
    -moz-border-top-colors: none;
    border-color: transparent;
    border-image: none;
    border-style: none;
    border-width: 0;
    bottom: 0;
    content: "";
    display: block;
    position: inherit;
    top: 0;
    z-index: 1;
}

.fmone-v2 .sidebar-menu > ul > li > ul.sub > li:before {
    border-top: none;
    content: "";
    display: inline-block;
    position:absolute;
    width: 0;
    margin: 0;
}

.fmone-v2 .sidebar-menu > ul > li > ul.sub {
    display: none;
    list-style: none;
    clear: both;
    padding-left: 0px;
}

.fmone-v2 .sidebar-menu > ul > li > ul.sub > li,
.fmone-v2 .sidebar-menu > ul > li > ul.sub .has-sub-sub ul.sub-sub > li {
    background: none;
    margin: 0px;
    padding: 0px;
    /* margin-top: 1px !important;*/
}

.fmone-v2 .sidebar-menu > ul > li > ul.sub > li > a {
    display: block;
    margin: 0px 0px 0px 0px;
    padding: 16px 15px 15px 10px;
    padding-left: 40px !important;
    color: #a4b8c6;
    text-decoration: none;
    font-size: 13px;
    font-weight: 300;
    background: none;
}

.fmone-v2 .sidebar-menu > ul > li > a {
    border-bottom: 0px solid #2f4050 !important;
    border-top: 1px solid #2f4050 !important;
    color: #a4b8c6 !important;
    text-shadow: 0 1px 0 #000000;
}

.fmone-v2 .sidebar-menu > ul > li.has-sub.open > a,
.fmone-v2 .sidebar-menu > ul > li > a:hover,
.fmone-v2 .sidebar-menu > ul > li:hover > a {
    background: #293846;
}

.fmone-v2 .sidebar-menu > ul > li > ul.sub > li.active > a,
.fmone-v2 .sidebar-menu > ul > li > ul.sub > li > a:hover,
.fmone-v2 .sidebar-menu > ul > li > ul.sub > li.current {
    background: #293846 !important;
}

.fmone-v2 .sidebar-menu > ul > li > ul.sub > li.current{
    background: #293846 !important;
    border-left: 4px solid #1ab394;
    text-indent: -4px;
}

.fmone-v2 .sidebar-menu > ul > li > ul.sub > li > a:hover {
    background: #293846 !important;
}

.container .fmone-v2 table.dataTable thead tr:first-child th {
    /*background-color: #ffffff;*/
    font-size: 14px;
    color: #666;
    /*border-bottom: 2px solid #e7e7e7;*/
    /*border-right: 0;*/
    text-align: left;
    /*font-weight: bold;*/
}

.container .fmone-v2 table.dataTable thead th,
.container .fmone-v2 table.dataTable thead td {
    border: 0;
    padding: 0px 10px 0px 10px;
}

.container .fmone-v2 table.dataTable.row-border tbody th,
.container .fmone-v2 table.dataTable.row-border tbody td,
.container .fmone-v2 table.dataTable.display tbody th,
.container .fmone-v2 table.dataTable.display tbody td {
    /*border: 0;*/
    padding: 10px 10px;
}

.container .fmone-v2 table.dataTable.display.editable tbody th,
.container .fmone-v2 table.dataTable.display.editable tbody td{
    padding-top:6px;
    padding-bottom:6px
}

.container .fmone-v2 .dataTables_wrapper .dataTables_paginate .paginate_button.current,
.container .fmone-v2 .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    border: 1px solid #1ab394;
    background-color: #1ab394;
}

.container .fmone-v2 .dataTables_wrapper .dataTables_paginate .paginate_button.disabled,
.container .fmone-v2 .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover,
.container .fmone-v2 .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:active {
    background-color: #f0f0f0;
    border: 1px solid #E5E5E5;
    color:#999 !important;
}

.container .fmone-v2 table.dataTable.display thead td {
    border-right: none;
}

.container .fmone-v2 .dataTables_wrapper .dataTables_paginate .paginate_button {
    margin: 0 5px;
    border-radius: 2px;
}

.container .fmone-v2 table.dataTable thead input {
    padding-right: 12px;
    font-size: 14px;
}

.container .fmone-v2 table.dataTable.display tbody tr:last-child{
    border-bottom: 0;
}

#content .fmone-v2 .s-button-tools .btn-1{
    color: #1ab394;
    background-color: #fff;
    border: 1px solid #1ab394;
}

#content .fmone-v2 .s-button-tools .btn-1:hover{
    color: #ffffff;
    background-color: #1ab394;
    border: 1px solid #1ab394;
}

#main-content .container .fmone-v2 table.dataTable.display tbody tr.odd > .sorting_1{
    background-color:#f1f1f1;
}

#main-content .container .fmone-v2 table.dataTable.display tbody tr.odd{
    background-color: #F3F4F4;
}

#main-content .container .fmone-v2 table.dataTable.display tbody tr.even{
    background-color: white;
}

#main-content .container .fmone-v2 table.dataTable.display tbody tr.even > .sorting_1{
    background-color: #f9f9f9;
}

#main-content .container .fmone-v2 table.dataTable.no-footer{
    /*border-bottom: 1px solid #E7EAEC;*/
    padding-bottom: 0;
}

.container .fmone-v2 .dataTables_paginate.paging_simple_numbers .paginate_button.previous{
    border: 1px solid #1ab394;
    color:#1ab394 !important;
}
.container .fmone-v2 .dataTables_paginate.paging_simple_numbers .paginate_button.next{
    border: 1px solid #1ab394;
    color:#1ab394 !important;
}

.container .fmone-v2 .dataTables_paginate.paging_simple_numbers .paginate_button.disabled, .container .dataTables_paginate.paging_simple_numbers .paginate_button.disabled:hover {
    color: #999999 !important;
    background-color: #f0f0f0;
    border: 1px solid #E5E5E5;
}

.container .fmone-v2 .dataTables_paginate.paging_simple_numbers .paginate_button:hover{
    border: 1px solid #1ab394;
    background: #1ab394;
    color: #1ab394 !important;
}

.checkboxdiv {
    width: 13px;
    height: 13px;
    display: inline-block;
    border: 1px solid #cccccc;
    background-color: #ffffff;
    top: 1px;
    cursor: pointer;
    position: relative;
    border-radius: 2px;
    margin:0;
}

/*表格checkbox样式*/
.checkboxdiv:after {
    opacity: 0;
    content: '';
    position: absolute;
    width: 7px;
    height: 4px;
    background-color: inherit;
    top: 3px;
    left: 2px;
    border: 2px solid #ffffff;
    border-top: none;
    border-right: none;
    -webkit-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    -o-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    transform: rotate(-45deg);
}

input[type=checkbox]:checked + .checkboxdiv {
    background-color: #1ab394;
    border: 1px solid #1ab394;
}

.checkboxdiv:hover::after {
    opacity: 1;
}

input[type=checkbox]:checked + .checkboxdiv:after {
    opacity: 1;
}

.bootbo.bootbox.fmone-v2 textarea.form-control{
    box-shadow: none;
    -webkit-box-shadow: none;
    width: 344px;
    min-height: 120px;
    font-size: 14px;
    border: 1px solid #e6e6e6;
    background: #FFFFFF;
}

.bootbox.fmone-v2 .modal-content .btn-primary-fo{
    min-width: 70px;
    height: 32px;
    color: #ffffff;
    background-color: #1ab394;
    border: 1px solid #1ab394;
    border-radius: 3px;
    font-size: 14px;
    margin: 0px;
    padding: 0px;
}

.bootbox.fmone-v2 .modal-content .btn-primary-fo:hover{
    background: #48c2a9;
    border-color: #48c2a9;
    color: #fff;
}

.bootbox.fmone-v2 .modal-content .btn-primary-fo:active{
    background: #17a185;
    border-color: #17a185;
    color: #fff;
}

.bootbox.fmone-v2 .modal-header{text-align: right}

/*.bootbox.fmone-v2 .modal-header > button:hover{
    background: url("../fmone-v2.1/images/dialog_x_hover.png") no-repeat;
}*/

.bootbox.fmone-v2 .modal-footer button.btn-success:hover,
.bootbox.fmone-v2 .modal-header button.btn-success:hover{
    background: #48c2a9;
    border: 1px solid #48c2a9;
}

.bootbox.fmone-v2 .modal-footer button.btn-success:active {
    background: #17a185;
    border-color: #17a185;
    color: #fff;
}

.bootbox.fmone-v2 .modal-header button.btn-default-fo:hover{
    border:1px solid #e6e6e6;
    background: #e6e6e6;
}

.bootbox.fmone-v2 .modal-header > button{ float: none;padding: 4px 18px; margin-top: -3px;margin-left: 10px;margin-right: 0;}
.bootbox.fmone-v2 .modal-header > button.close{ opacity: 1;float:right; border:0; padding:0; margin-top:0; color:#9e9e9e;}
.bootbox.fmone-v2 .modal-header > button.close:hover{ opacity: 1; color:#ff5353}

.container table.dataTable.display tbody tr.odd:hover > .sorting_1,
.container table.dataTable.display tbody tr.even:hover > .sorting_1,
#main-content .container .fmone-v2 table.dataTable.display tbody > tr.odd:hover {
    background-color: #F3F4F4;
}

#main-content .container .fmone-v2 table.dataTable.display .dataTables_empty{
    background-color: #F3F4F4;
    padding:10px;
    color:#999;
}

#main-content .container .fmone-v2 table.dataTable.display .dataTables_empty:before{
    content:" ";
    background-image: url(../../img/icon_notice.png);
    background-repeat: no-repeat;
    width: 18px;
    height: 18px;
    display: inline-block;
    vertical-align: bottom;
    margin-right: 14px;
}

@media screen and (min-width: 768px) {
    .bootbox.fmone-v2 .modal-dialog {
        margin-top: 8%;
    }
}

.bootbox.fmone-v2 .modal-header{
    background-color: #f2f2f2;
    color: #666666;
    height: 43px;
    font-size: 16px;
    padding: 10px 15px;
}

.bootbox.fmone-v2 .modal-title{
    font-size: 16px;
}

.fmone-v2 .sidebar-menu > ul > li > ul.sub > li.current > a{
    color:#ffffff;
}

.bootbox.fmone-v2 .modal-footer{
    padding:0 30px;
}

.bootbox.fmone-v2 .modal-footer .btn{
    margin-top:0;
    margin-bottom:20px;
    margin-left:10px;
    margin-right:0;
}

.bootbox.fmone-v2 .modal-footer .btn + .btn{  }

.fmone-v2 .dropzone,
.container .fmone-v2 .dropzone{
    border:none; background-color: #fff;
}

.bootbox.fmone-v2 .dataTables_wrapper{border:none; padding:0}

.bootbox.fmone-v2 table.dataTable.no-footer{
    border: 1px solid #e0e0e0;
    border-bottom: none;
    padding-bottom: 0px;
}

.bootbox.fmone-v2 table.dataTable thead th,
.bootbox.fmone-v2 table.dataTable thead td,
.bootbox.fmone-v2 table.dataTable tbody th,
.bootbox.fmone-v2 table.dataTable tbody td,
.bootbox.fmone-v2 table.dataTable thead tr:last-child td{
    padding:6px 10px;
}

.bootbox.fmone-v2 table.dataTable tbody th,
.bootbox.fmone-v2 table.dataTable tbody td{
    border-bottom: 1px solid #e0e0e0;
}


.bootbox.fmone-v2 table.dataTable thead tr:first-child{
    background-color: #E1E4E6;
    color:#666;
}

.bootbox.fmone-v2 table.dataTable thead tr:first-child th{
    background-color: transparent;
    font-weight:bold;
    text-align: left;
    border-right: 1px solid #ccc;
}
.bootbox.fmone-v2 table.dataTable thead tr:first-child th:last-child{border-right:0}

.bootbox.fmone-v2 table.dataTable.display tbody tr.even,
.bootbox.fmone-v2 table.dataTable.display tbody tr.odd{background-color: #fff}

.bootbox.fmone-v2 table.dataTable.display tbody tr.even:hover,
.bootbox.fmone-v2 table.dataTable.display tbody tr.odd:hover{background-color: #f3f3f4;}

.bootbox.fmone-v2 .dataTables_wrapper .dataTables_paginate .paginate_button{margin:0 5px; border-radius: 3px}
.bootbox.fmone-v2 .dataTables_wrapper .dataTables_paginate .paginate_button.next{margin-right:0;    border: 1px solid #1ab394;color: #1ab394 !important;}
.bootbox.fmone-v2 .dataTables_wrapper .dataTables_paginate .paginate_button.next:hover{
    color: #1ab394 !important;
}

.bootbox.fmone-v2 .dataTables_wrapper .dataTables_paginate .paginate_button.disabled{
    color: #999999 !important;
    background-color: #f0f0f0;
    border: 1px solid #E5E5E5;
}
.bootbox.fmone-v2 .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover{
    color: #999999 !important;
}

.bootbox.fmone-v2 .dataTables_wrapper .dataTables_info{padding-left:0}

.container .fmone-v2 .dataTable .ms-ctn input, .modal-open .ms-ctn input{height: 28px;}
.container .fmone-v2 .dataTable .ms-ctn .ms-trigger .ms-trigger-ico{    margin-top: 12px;margin-left: 9px;}

.fmone-v2 table.popup-label-table{width:100%;margin-bottom:20px}
.fmone-v2 table.popup-label-table td:first-child{padding-left:0;}
.fmone-v2 table.popup-label-table td {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    line-height:30px;
    display: inline-block;
    padding-left:10px;
}

.fmone-v2 .dropdown-menu{max-width: none;}
.fmone-v2 hr{
    border-top:1px solid #E7EAEC;
    margin-top: 22px;
    margin-bottom: 22px;
}

.container .fmone-v2 table tr.table-search i.t-search,
.bootbox.fmone-v2  table tr.table-search i.t-search{
    background: url(../../img/icon_search.png) no-repeat;
    background-position:0 0;
    margin-top: 0;
    width: 35px;
    height: 100%;
    z-index: 10;
    position: absolute;
    right: 2px;
    cursor: pointer;
    top: 11px;
}

.container .fmone-v2 table tr.table-search i.t-search{ top: 14px; }

.fmone-v2 table.dataTable.display tbody{
    font-size: 14px;
}
.container .dropzone .dz-file-preview .dz-remove i {
    cursor: pointer;
}

.fmone-v2 .dropzone a.dz-remove{
    background-color: transparent;
    color: #e25856;
    font-size: 20px;
    padding: 0;
    border-radius: 0;
}

.fmone-v2 .dropzone .dz-file-preview .dz-file-notice{
    position: absolute;
    top: -8px;
    right: -20px;
}

.fmone-v2 .dropzone a.dz-remove:hover{
    background: #e25856;
    color: #fff;
}
