.report-container{ padding:19px 0; }
.report-container .row{
    margin-left:-5px;
    margin-right:-5px;
}
.report-container .col-sm-1,
.report-container .col-sm-2,
.report-container .col-sm-3,
.report-container .col-sm-4,
.report-container .col-sm-5,
.report-container .col-sm-6,
.report-container .col-sm-7,
.report-container .col-sm-8,
.report-container .col-sm-9,
.report-container .col-sm-10,
.report-container .col-sm-11,
.report-container .col-sm-12{
    padding-left:5px;
    padding-right:5px;
}

.report-container .col-md-1,
.report-container .col-md-2,
.report-container .col-md-3,
.report-container .col-md-4,
.report-container .col-md-5,
.report-container .col-md-6,
.report-container .col-md-7,
.report-container .col-md-8,
.report-container .col-md-9,
.report-container .col-md-10,
.report-container .col-md-11,
.report-container .col-md-12{
    padding-left:5px;
    padding-right:5px;
}

/* panel block */
.report-panel{
    border-top:3px solid #e7eaec;
    background: #fff;
    color:#666;
}
.report-panel-head{
    padding:0 20px;
    height: 50px;
    line-height: 50px;
    border-bottom: 1px solid #e7eaec;
}
.report-panel-body{
    padding:20px;
}

.report-panel-head-right { float:right; }
#content .report-panel-head-right .btn{margin-top:-3px;}
#content .report-panel-head-right .btn:last-child{margin-right:0;}

.report-flex-box{
    display:flex;
}
.report-flex-1{
    flex:1;
    margin-left:15px;
}

.report-flex-vertical{
    margin-top:auto;
    margin-bottom:auto;
}

.report-flex-horizontal{
    margin-left:auto;
    margin-right:auto;
}

.report-flex-1:first-child{ margin-left:0}

.report-panel-block{
    padding: 14px 20px;
    border-top:3px solid #e7eaec;
    background: #fff;
    color:#666;
}

.image-icon{
    width:16px; 
    height:16px;
    display:inline-block;
    line-height:1;
    vertical-align: baseline;
    padding: 1px 10px;
    background-repeat: no-repeat;
}
.image-icon:before{content:"0"; color:transparent}

.icon-pending-requirement{ background-image: url('../img/report/icon-pending-requirement.png')}
.icon-feedback{ background-image: url('../img/report/icon-feedback.png')}
.icon-pending-workorder{ background-image: url('../img/report/icon-pending-workorder.png')}
.icon-approval{ background-image: url('../img/report/icon-approval.png')}
.icon-validate{ background-image: url('../img/report/icon-validate.png')}
.icon-archive{ background-image: url('../img/report/icon-archive.png')}
.icon-inspection{ background-image: url('../img/report/icon-inspection.png')}
.icon-calendar{background-image: url('../img/report/icon-calendar.png')}


/* panel block */
.report-panel-block .badge{
    width: 50px;
    height: 20px;
    text-align: center;
    font-size: 14px;
    font-weight: normal;
    padding: 4px 7px;
}
.report-panel-block .badge-dark-green{
    background-color:#1ab394
}

.report-panel-block .badge-green{
    background-color:#90c458
}

.report-panel-block .badge-orange{
    background-color:#ffa13d
}

.report-panel-block .badge-blue{
    background-color:#149bd9
}

.report-panel-block .badge-red{
    background-color:#ff5b5b;
}

/* 琛ㄦ牸 */
.report-table{
    font-size: 12px;
   /* font-family: '宋体';*/
    color:#666666;
}

.report-table.table-bordered{
    border-color:#eaeaea;
}

.report-table.table-bordered > thead > tr > th,
.report-table.table-bordered > tbody > tr > th,
.report-table.table-bordered > tbody > tr > td{
    border-color:#eaeaea;
}

.report-table.table-bordered > thead > tr>th{
    border-bottom-width: 1px;
    border-bottom-color:transparent;
}

.report-table.table > thead > tr > th,
.report-table.table > tbody > tr > th,
.report-table.table > tbody > tr > td{
    padding-left:15px;
}

.report-table>thead th{
    background-color:#f2f2f2;
    font-weight:normal;
}

.report-table>tbody th{
    font-weight:normal;
    color:#1c83c6;
}

.report-table a{
    color:#1c83c6;
}

.report-table>tbody td.selected{
    background-color:#fafafa;
}

.report-table>tbody tr.selected{
    background-color:#fafafa;
}

.report-table.line-selectable>tbody tr{
    cursor: pointer;
}

.report-action-td{
    cursor:pointer;
}
.report-action-td:hover{
    background-color:#fafafa;
}

/* 按钮 */
.report-btn{
    display: inline-block;
    margin-bottom: 0;
    margin-right: 10px;
    font-weight: normal;
    text-align: center;
    vertical-align: baseline;
    cursor: pointer;
    background-image: none;
    border: 1px solid transparent;
    white-space: nowrap;
    padding: 4px 19px;
    font-size: 14px;
    line-height: 1.428571429;
    border-radius: 3px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -o-user-select: none;
    user-select: none;
}

.report-panel-head .report-btn{
    margin-top:9px;
}

.report-btn:last-child{
    margin-right:0
}

.report-btn-success,.report-btn-success.btn:focus{
    color:#1ab394;
    border:1px solid #1ab394;
    background: #fff
}

.report-btn-success:hover{
    color:#fff;
    background: #1ab394
}

.report-btn-warning,.report-btn-warning.btn:focus{
    color:#ff9e36;
    border:1px solid #ff9e36;
    background: #fff
}

.report-btn-warning:hover{
    color:#fff;
    background: #ff9e36
}

/* 分页 */
.report-pagination{
	display: inline-block;
    padding: 0px;
    padding-top:13px;
    padding-right: 16px;
    margin: 0;
    border-radius: 3px;
}
.report-pagination>li{
	display: inline;
}
.report-pagination>li>a{
	position: relative;
    float: left;
    padding: 0px 12px;
    margin-left: -1px;
    line-height: 1.42857143;
    color: #a8a8a8;
    text-decoration: none;
    background-color: #fff;
    border: 1px solid #ddd;
}
.report-pagination>li>a:hover{
	color: #1ab394;
}

/* 报表 */
.report-form{
	margin-top: 50px;
}
.report-form>h2{
	
	text-align: center;
	color: #333;
	font-size: 30px;
}
.report-form>h4{
	color: #4c4c4c;
	font-size: 18px;
	text-align: center;
	margin-top: 22px;
	margin-bottom: 60px;
}
.report-form>h3{
	font-size: 14px;
    color: #4c4c4c;
    font-weight: bold;
    border-left: 3px solid #1ab394;
    margin-left: 20px;
    padding-left: 10px;
    margin-bottom: 20px;
}
.report-title{
	background-color: #EBEBEB;
	height: 32px;
	line-height: 32px;
	margin-left: 20px;
	margin-right: 20px;
	padding-left: 20px;
    font-weight: bold;
    color: #4c4c4c;
    font-size: 14px;
    margin-bottom: 50px;
}
.report-content{
	margin:50px 20px
}
.report-article{
	font-size: 14px;
	color: #666666;
	margin-left: 100px;
}


/* 日历 */
.report-panel .fc-center{
    padding-top: 8px;
    
}

.report-panel .fc-center h2{
    font-size: 14px;
}

.report-panel .fc td,
.report-panel .fc th{
    border-width: 0;
}

.report-panel .fc-event{
    text-align:center;
    background-color: #1ab394;
    border:none;
    border-radius: 6px;
    cursor:pointer;
}

.report-panel .fc-event .fc-content{
    transform: scale(0.8);
}

.report-panel .fc table th,
.report-panel .fc table td {
    color:#666; 
    font-weight: normal;
    
}

.report-panel .fc-ltr .fc-basic-view .fc-day-number{
    text-align: center;
    cursor:pointer;
}

.report-panel .fc-prev-button,
.report-panel .fc-next-button{
    background: none;
    border: none;
    box-shadow: none;
}
.report-panel .fc-basic-view .fc-body .fc-row{
    min-height: 3em;
}

.container .report-panel .fc-unthemed .fc-day:hover{
    background-color:#e2ecf6;
}

.container .report-panel .fc-unthemed .fc-day.state-highlight{
    background-color:#e2ecf6;
    cursor:pointer;
}

.report-panel .fc-icon-left-single-arrow:after{content:" "}

.report-panel .fc button .fc-icon.fc-icon-left-single-arrow{
    color:#666;
    background:url('../img/icon-left-arrow.png') no-repeat;
    background-position: 50%;
}

.report-panel .fc-icon-right-single-arrow:after{content:" "}

.report-panel .fc button .fc-icon.fc-icon-right-single-arrow{
    color:#666;
    background:url('../img/icon-right-arrow.png') no-repeat;
    background-position: 50%;
}

.report-panel .fc button.fc-state-hover .fc-icon.fc-icon-left-single-arrow{
    background:url('../img/icon-left-arrow.png') no-repeat;
    background-position: 50%;
}

.report-panel .fc button.fc-state-hover .fc-icon.fc-icon-right-single-arrow{
    background:url('../img/icon-right-arrow.png') no-repeat;
    background-position: 50%;
}

.report-panel .fc-day-number.fc-other-month{
    opacity: 1;
    color:#b2b2b2;
}

.report-panel .fc-today.fc-state-highlight{
    background: #AACEFF!important;
    border-radius: 9px;
    color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
}

.report-panel .fc-state-highlight{
    background: #e2ecf6
}

.container .report-panel .fc-unthemed .fc-head{
    background-color:transparent;
}

/* 布局样式 */
.report-h300{
    height:300px;
}
.report-h200{
    height:200px;
}
.report-h100{
    height:100px;
}
.report-layout-table{
    display:table;
}
.report-layout-table-cell{
    display: table-cell;
    vertical-align: middle;
}

.report-width-80percent{
    width:80%;
    margin-left:auto;
    margin-right:auto;
}
.report-width-90percent{
    width:90%;
    margin-left:auto;
    margin-right:auto;
}

.report-last-update{
    color: #999;
    font-size: 12px;
    padding-bottom: 15px;
}
.report-last-update i.fa{margin-right:8px}

.report-nodata-img{
    margin: auto;
    display: block;
    padding-top: 35px;
}
.report-nodata-label{
    display: block;
    margin: 10px auto;
    text-align: center;
}
.report-chart-loading{
    height: 100%;
    display: flex;
    background: #fafafa;
}

.report-chart-loading-inner{
    flex: 1;
    text-align: center;
    margin: auto;
    font-size: 24px;
}

@media screen and (min-width: 1024px) and (max-width: 1440px) {
    .report-table.table > thead > tr > th,
    .report-table.table > tbody > tr > th,
    .report-table.table > tbody > tr > td{
        padding-left:8px;
    }
}