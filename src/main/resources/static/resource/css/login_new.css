.login__background{
    position: fixed;
    top:0;
    left:0;
    width: 100%;
    height: 100%;
    min-width: 600px;
    z-index: -10;
    background: url(../img/loginbg.jpg) no-repeat 0 0;
    background-position: center center;
    background-size: cover;
    zoom: 1;
}
.login__form{
    position: fixed;
    top: 0;
    width: 356px;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.4);
}
.login__form-lan{
    left: 20px;
    top: 20px;
    position: absolute;
    border: 1px solid transparent;
    border-radius: .25rem;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    width: 112px;
    cursor: pointer;
}
.login__form-info{
    position: absolute;
    top: 26%;
    left: 46px;
}
.login__form-logo{
    width: 265px;
    height: 80px;
    position: relative;
    text-align: center;
    padding: 10px 28px;
    line-height: 84px;
    padding-top: 4px;
}
.login__form-info-group{
    background: rgba(0, 0, 0, 0.2);
    float: left;
    margin-bottom: 25px;
    border-radius: 17px;
    width: 264px;
    border: 1px solid transparent;

}
.login__form-info-input{
    background: transparent;
    border: 0;
    height: 32px;
    color: #fff !important;
    outline:none;
    width: 67%;
}
.login__form-info-icon{
    color: rgba(255,255,255,0.8);
    border: 0;
    width: 32px;
    height: 32px;
    margin: 0 10px;
    padding: 0;
    background: transparent;
    border-radius: 17px;
    text-align: center;
    float: left;
    line-height: 29px;
}
:-webkit-autofill {
    -webkit-text-fill-color: #fff !important;
    transition: background-color 500000000s ease-in-out 0s;
}
.login__form-info-input::-webkit-input-placeholder{
    color:#fff;
}
.login__form-info-input::-moz-placeholder{   /* Mozilla Firefox 19+ */
    color:#fff;
}
.login__form-info-input:-moz-placeholder{    /* Mozilla Firefox 4 to 18 */
    color:#fff;
}
.login__form-info-input:-ms-input-placeholder{  /* Internet Explorer 10-11 */
    color:#fff;
}
.login__form-group{
    width: 264px;
    float: left;
    margin-top: 25px;
}
.login__form-info-submit{
    background-color: rgba(26,179,148,0.8);
    color: #fff;
    border-radius: 20px;
    width: 100%;
    height: 36px;
    border: 1px solid transparent;
    outline:none;
    line-height: 33px;
    cursor: pointer;
}
.login__form-lan-span{
    color: #fff;
}
.login__form-lan-span>.icon_arrow-down{
    margin-left: 6px
}
.login__form-group-arrow {
    height: 25px;
    width: 25px;
    border-radius: 12.5px;
    background-color: #1b947b;
    float: right;
    margin-top: 4px;
    line-height: 25px;
}
.nav-dropdown{
    padding: 10px 0;
    margin: 5px 0;
    background-color: #fff;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    box-shadow: 0 4px 13px 0 rgba(0, 0, 0, .2);
    list-style: none;
    display: none;
    position: relative;
    z-index: 1;
}
.popper__arrow{
    top: -6px;
    left: 50%;
    margin-right: 3px;
    border-top-width: 0;
    border-bottom-color: #ebeef5;
    border-width: 6px;
}
.popper__arrow::after{
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
    content: " ";
    border-width: 6px;
    top: -6px;
    left: 50%;
    margin-left: -6px;
    border-top-width: 0;
    border-bottom-color: #fff;
}
.nav-dropdown__item{
    list-style: none;
    line-height: 36px;
    padding: 0 20px;
    margin: 0;
    font-size: 14px;
    color: #666;
    cursor: pointer;
    outline: 0;
}
.nav-dropdown__item:hover{
    background-color:#e8f8fd;
    color:#1c83c6
}
.closeX{
    position:absolute;
    font-size:25px;
    font-weight:bold;
    line-height:1;
    color:#000;
    text-shadow: 0 1px 0 #fff;
    border:none;
    opacity:.2;
    right:0;
    top:12px;
}
.agreeTip{
    display: inline-block;
    height:50px;
    float:left;
    text-align: left;
    padding: 0px 10px;
}
.tip-check{
    display: inline-block;
    margin-right: 6px;
    text-align: left;
    width: 16px;
}
.tip-text{
    display: inline-block;
    font-size:14px ;
    font-weight: 600;
    text-align: left;
}
.button-group{
    float:right;
}
.btn-cancle{
    width: 100px;
    margin-right:20px;
}
.btn-confirm{
    width: 100px;
    cursor: default;
    background-color: #2097d0;
    color:#fff;
}
.btn-confirm[disabled]{
    opacity: .5;
    background-color: #62bce3;
}
.btn:focus,
.btn:active:focus,
.btn.active:focus,
.btn.focus,
.btn:active.focus,
.btn.active.focus {
    outline: none;
    border-color: transparent;
    box-shadow:none;
}
.errorMsg{
    color:#f00;
    margin-top:-10px;
    margin-left:25px;
}
.modal .modal-body {
    max-height: 500px;
    overflow-y: auto;
}
.modal .modal-body .agreementswipper div{
    font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Segoe UI';
    font-size: 15px;
    letter-spacing:normal;
    overflow-wrap: break-word;
    text-align: justify;
    line-height:26.1px;
    margin-bottom:6px;
}
.modal .modal-dialog{
    width: 800px;
}
::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 4px; /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;
}
::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 2px;
    box-shadow: inset 0 0 5px rgba(97, 184, 179, 0.1);
    background: #999;
}
::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    box-shadow: inset 0 0 5px rgba(87, 175, 187, 0.1);
    border-radius: 10px;
    background: #ededed;
}
