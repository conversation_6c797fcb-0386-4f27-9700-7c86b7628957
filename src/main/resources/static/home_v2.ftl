<!DOCTYPE html>
<#assign  _STATIC_COMMON_="${baseStaticPath}/resource">
<#assign  _STATIC_BUSINESS_="${baseStaticPath}/business">
<#assign  _STATIC_VERSION_="?_v="+.now?string("yyyyMMdd")>
<#assign  _NEW_STYLE_="fmone-v2">

<html>
<style>
    .not-full-screen #header, .not-full-screen .header {
        display: none !important;
    }
    .not-full-screen #sidebar, .not-full-screen .menu {
        display: none !important;
    }
    .not-full-screen .nav.nav-tabs {
        display: none !important;
    }
    .not-full-screen #page #main-content {
        padding-top: 0 !important;
        margin-left: 0 !important;
    }
    .el-message-box__wrapper {
        z-index: 19999 !important;
    }
</style>
<script>
    (function(){
        var reg = new RegExp("(^|&)"+ "mode" +"=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if(r !== null){
            document.getElementsByTagName('html')[0].setAttribute('class', 'not-full-screen');
        }
    })()
</script>
<#include "template/head_v2.ftl" encoding="utf8">
<body>
<#include "template/header_v2.ftl" encoding="utf8">
<div class="main-content no-visiable">
    <#include "template/left_v2.ftl" encoding="utf8">
    <div id="VueElement_MainContent"  class="container">
        <div class="row" style="background-color: #f5f5f5;">
            <!-- notice-->
            <div id="index_notice" style="position: fixed;  z-index: 9999;margin-top: -50px;"></div>
            <!--header-->
            <div id="page-header" style="display: none;" data-home="${i18n("page.index.content.home")}"><div class="page-header" style="min-height: 0;"><h3 style="margin: 0px;">${i18n("page.index.content.home")}</h3></div></div>
            <!--content-->
            <div id="content" class="page-content">
                <el-scrollbar  :wrap-style="[{
                        height: contentHeight + 'px',
                        'overflow-x':'hidden'
                    }]" :max-height="contentHeight" ref="contentScroller">
                    <#--<div class="page-content-loading">加载中...</div>-->
                    <div class="page-content-container"></div>
                </el-scrollbar>
            </div>
        </div>
    </div>
</div>
<script>document.documentElement.lang="${i18nLocale}";</script>
<#-- xss -->
<script src="${_STATIC_COMMON_}/js/js-plugin/xss/xss.min.js"></script>
<script src="${_STATIC_COMMON_}/js/js-plugin/stomp/stomp.js"></script>
<#include "template/footer_v2.ftl" encoding="utf8">

<script>
    //消息服务配置参数
    var _messagesite_hostname = "${messagesite.hostname}";
    var _messagesite_port = "${messagesite.port?c}";
    var _messagesite_channel = "${messagesite.chanel}";
</script>
<script>
    var menu = document.querySelector("#VueElement_LeftMenu");
    menu.style.height = document.documentElement.clientHeight - 60 +'px';

    var pageContent = document.querySelector(".page-content");
    pageContent.style.height = document.documentElement.clientHeight - 60 +'px';

    window.addEventListener('resize', function(){
        var menu = document.querySelector("#VueElement_LeftMenu");
        menu.style.height = document.documentElement.clientHeight - 60 +'px';

        var pageContent = document.querySelector(".page-content");
        pageContent.style.height = document.documentElement.clientHeight - 60 +'px';
    });

    require(['vue','init'],function(Vue, init) {
        init.header.$on('narrow-changed',function(isNarrow){
            if(isNarrow){
                $('.main-content').addClass('main-content-narrow');
            }
            else{
                $('.main-content').removeClass('main-content-narrow');
            }
        });

        var vm = new Vue({
            el:"#VueElement_MainContent",
            data:{
                contentHeight:document.documentElement.clientHeight - 61,
                isNarrow:false
            },
            mounted:function(){
                init.menu.$emit('home-load');
                var me = this;
                window.addEventListener('resize', function(){
                    me.contentHeight=document.documentElement.clientHeight - 61
                });
            }
        });
        if ("${headPic!}" != "") {
            var path="${headPic!}";
            $(".header-avatar-image").find("img").attr("src", path);
            $("#header-user .dropdown-toggle").find("img").attr("src", path);
            $(".header-user-image").attr("src", path);
        }

        var openProjectHome = function (pid) {
            var accesstoken = localStorage.access_token;
            if (!accesstoken) {
                accesstoken = FO.get_uri_param("access_token");
            }
            localStorage.current_project = pid;

            //百度追踪
            _hmt && _hmt.push(['_trackPageview', "/main/home/" + pid + "?current_project=" + pid]);

            location.href = location.origin + "/main/home/" + pid + "?current_project=" + pid;
        }

        //tohome
        var _pone = "${isem?c}";
        if (_pone == "true") {
        <#if pcurrent??>
            // openProjectHome("${pcurrent.id?c}"); TODO:完成改版后开启
        </#if>
        }

        //click to home
        $(".projects .project").click(function () {
            var _pid = $(this).data("pid");

            openProjectHome(_pid);
        });
        $(".projects .project img").each(function () {
            var _pid = $(this).data("pic");
            if (_pid) {
                var _src = PUBLIC_PATH+"/common/files/id/" + _pid + "/img";
                $(this).attr("src", _src);
            }
        });
        //});
        window.showMeaageSite = function () {
            openMenuPage("/uc002", {});
        }
        window.showPerson = function (e) {
            openMenuPage("/uc001", {});
        }
        window.exit = function () {
            $.get("/logout", function (data) {
                if (coeEnabled == "true") {
                    //百度追踪
                    _hmt && _hmt.push(['_trackPageview', coeUrl]);

                    window.location.href = coeUrl;
                } else {
                    var href = PUBLIC_PATH + "/login";

                    //百度追踪
                    _hmt && _hmt.push(['_trackPageview', href]);

                    window.location.href = href;
                }
            });
        }

    });
</script>

<audio id="noticeMp3" src="${_STATIC_COMMON_}/img/notice.mp3" hidden="true"></audio>
</body>
</html>
