<!DOCTYPE html>
<html lang="en">
<head>
</head>
<body>
	<!-- JAVASCRIPTS -->
	<!-- Placed at the end of the document so the pages load faster -->
	<!-- JQUERY -->
	<script src="resource/js/js-plugin/jquery/jquery-2.0.3.min.js"></script>
	<!-- BOOTSTRAP -->
	<script src="resource/js/facilityone.js"></script>

	<script>
	var timeZoneOffset = (new Date()).getTimezoneOffset();
	timeZoneOffset = -timeZoneOffset/60;
	
		jQuery(document)
				.ready(
						function() {
							$
									.ajax({
										url : 'oauth2/token',
										type : "POST",
										data : {
											"client_id" : "00000000",
											"client_secret" : "11111111",
											"grant_type" : "authorization_code",
											"code" : FO.get_uri_param("code"),
											"i18n" : FO.get_uri_param("i18n"),
											"zone":timeZoneOffset,
											"redirect_uri" : "main_v2/index"
										},
										success : function(response) {
											if (response.access_token) {
												if (localStorage.from) {
													window.location = localStorage.from;
												} else {
													window.location = "main_v2/index#__aurl=%2Fchart%2Fdashboard%2Findex_new";
												}

											}
										}
									});
						});
	</script>
	<!-- /JAVASCRIPTS -->
</body>
</html>
