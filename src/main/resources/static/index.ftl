<!DOCTYPE html>
<#assign  _STATIC_COMMON_="${baseStaticPath}/resource">
<#assign  _STATIC_BUSINESS_="${baseStaticPath}/business">
<#assign  _STATIC_VERSION_="?_v="+.now?string("yyyyMMdd")>

<html>
<#include "template/head.ftl" encoding="utf8">
<style>
    .btn-hidden{
        display: none !important;
    }
    .projects {
        margin-top: 15px;
    }

    .projects .project {
        border: 1px solid #dddddd;
        max-width: 350px;
        max-height: 240px;
        padding: 10px;
        float: left;
        margin: 0 10px;
    }

    .projects .project span {
        float: right;
    }

    .projects .project img {
        max-width: 100%;
    }

    #noPermission {
        height: 40vh;
        text-align: center;
        line-height: 40vh;
        min-height: 200px;
        font-weight: 600;
        font-size: 22px;
        position: absolute;
        width: 90%;
    }
</style>
<body>
<#include "template/header.ftl" encoding="utf8">
<section id="page">
<#include "template/left.ftl" encoding="utf8">
    <div id="main-content" class="">
        <div class="container">
            <div class="row" style="background-color: #f5f5f5;">
                <!-- notice-->
                <div id="index_notice" style="position: fixed;  z-index: 9999;"></div>
                <!--header-->
                <div id="page-header" style="display: none;" data-home="${i18n("page.index.content.home")}">
                    <div class="page-header" style="min-height: 0;"><h3
                            style="margin: 0px;">${i18n("page.index.content.home")}</h3></div>
                </div>
                <!--content-->
                <div id="noPermission" style="display: none;">
                    <div style="height: 100px;">
                        <svg t="1698308804254" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3326" width="64" height="64"><path d="M512 1024C229.232 1024 0 794.768 0 512 0 229.232 229.232 0 512 0 794.768 0 1024 229.232 1024 512 1024 794.768 794.768 1024 512 1024ZM560 240C560 213.488 538.512 192 512 192 485.488 192 464 213.488 464 240L464 592C464 618.512 485.488 640 512 640 538.512 640 560 618.512 560 592L560 240ZM512 736C485.488 736 464 757.488 464 784 464 810.512 485.488 832 512 832 538.512 832 560 810.512 560 784 560 757.488 538.512 736 512 736Z" fill="#117cee" p-id="3327"></path></svg>
                    </div>
                    您无此系统操作权限，请联系管理员
                </div>
                <div id="content" class="col-lg-12">

                </div>
            </div>
            <div class="footer-tools" style="display:none;">
                    <span class="go-top">
                        <i class="fa fa-chevron-up"></i> Top
                    </span>
            </div>
        </div>
    </div>
</section>
<script>document.documentElement.lang = "${i18nLocale}";</script>
<#include "template/footer.ftl" encoding="utf8">
<script src="${_STATIC_COMMON_}/js/jquery-plugin/jquery-plugin-hash.js"></script>
<script src="${_STATIC_COMMON_}/js/js-plugin/navmaster/jquery.nav.js"></script>
<script src="${_STATIC_COMMON_}/js/js-plugin/navmaster/jquery.scrollTo.js"></script>
<script src="${_STATIC_COMMON_}/js/js-plugin/ezuiplayer/ezuiplayer.js"></script>
<script src="${_STATIC_COMMON_}/js/js-plugin/stomp/stomp.js"></script>
<#-- xss -->
<script src="${_STATIC_COMMON_}/js/js-plugin/xss/xss.min.js"></script>

<script type="text/javascript" src="${_STATIC_COMMON_}/js/js-plugin/jquery-city-select/data.js"></script>

<script>
    //消息服务配置参数
    var _messagesite_hostname = "${messagesite.hostname}";
    var _messagesite_port = "${messagesite.port?c}";
    var _messagesite_channel = "${messagesite.chanel}";

    var progros = "${progros?size}",
        isEm = ${isem?c};

    if(progros == '0' && isEm) {
        $('#noPermission').show();
        $('#content').hide();
    }
</script>

<script>
    $(document).ready(function () {

        //doFixButtonGroup(".button-bar");

        //headpic
        if ("${headPic!}" != "") {
            var path = "${headPic!}";
            $("#header-user .dropdown-toggle").find("img").attr("src", path);
            $(".header-user-image").attr("src", path);
        }

        var openProjectHome = function (pid) {
            localStorage.current_project = pid;

           //百度追踪
            _hmt && _hmt.push(['_trackPageview', "/main/home/" + pid + "?current_project=" + pid]);

            location.href = location.origin + "/main/home/" + pid + "?current_project=" + pid;
        }

        //tohome
        <#--var _pone = "${isem?c}";-->
        <#--if (_pone == "true") {-->
        <#--<#if pcurrent??>-->
        <#--    openProjectHome("${pcurrent.id?c}");-->
        <#--</#if>-->
        <#--}-->

        //click to home
        $(".projects .project").click(function () {
            var _pid = $(this).data("pid");

            openProjectHome(_pid);
        });
        $(".projects .project img").each(function () {
            var _pid = $(this).data("pic");
            if (_pid) {
                var _src = PUBLIC_PATH+"/fm/file/" + _pid + "/info";
                $(this).attr("src", _src);
            }
        });
    });
    var showMeaageSite = function () {
        openMenuPage("/uc002", {});
    }
    var showPerson = function (e) {
        openMenuPage("/uc001", {});
    }
    var shwoHelpCenter=function(e){
        window.open('https://help.fmone.cn')
    }
    var exit = function () {
        $.get("/logout",function(data){
            if (coeEnabled == "true") {
                //百度追踪
                _hmt && _hmt.push(['_trackPageview', coeUrl]);

                window.location.href = coeUrl;
            } else {
                var href = PUBLIC_PATH + "/login";

                //百度追踪
                _hmt && _hmt.push(['_trackPageview', href]);

                window.location.href = href;
            }
        });
    }
</script>

<script src="${_STATIC_COMMON_}/js/index.js"></script>

<audio id="noticeMp3" src="${_STATIC_COMMON_}/img/notice.mp3" hidden="true"></audio>

<script>
    websocketNoticeLogOff();

    // websocket 通知登出
    function websocketNoticeLogOff(){
        var userId = $("#user_id").val();
        var protocol = 'ws://';
        if (window.location.protocol === 'https:') {
            protocol = 'wss://';
        }

        var url = protocol + location.hostname +":" + _messagesite_port+ PUBLIC_PATH +'/stomp/websocket' ;
        if(location.port == 0 || 2 == location.port.length){
            url = protocol + location.hostname + PUBLIC_PATH +'/stomp/websocket';
        }

        var websocket = new WebSocket(url);
        stompClient = Stomp.over(websocket);
        stompClient.connect({}, function(frame) {
            // 订阅目标
            stompClient.subscribe('/user/'+userId+'/logoff', function(data) {
                var data = JSON.parse(data.body)

                // 如果服务器通知下线，而且通知的webcid和当前浏览器cookie的webcid相等，就弹框提示下线。后台清空 cookie 和 localstorage
                const webcid = getCookie('webcid');

                if (data.channel == "logoff" && (data.customs && data.customs.webcid == webcid)) {
                    clearLocalStorage();
                    clearCookie();

                    var tipText = '${i18n("js.vendor001.notice")}'

                    bootbox.dialog({
                        message: '<div id="job_form_dialog">' + data.content + '</div>',
                        title: tipText,
                        buttons: {
                            success: {
                                className: "btn-success",
                                callback: function () {
                                    window.location.href = coeUrl;
                                }
                            },
                            cancel:{
                                className: "btn-hidden"
                            }
                        }
                    });
                }
            });
        }, function(error) {
            // 连接失败回调
            console.log('Error: ' + error);
        });
    }

    // 获取cookie
    function getCookie(name) {
        // 将 Cookie 字符串拆分为键值对
        const cookies = document.cookie.split(';');

        // 遍历所有 Cookie
        for (let cookie of cookies) {
            // 去除空格
            cookie = cookie.trim();

            // 检查是否以目标字段开头
            if (cookie.startsWith(name + '=')) {
                // 返回字段值
                return cookie.substring(name.length + 1);
            }
        }

        // 如果未找到，返回 null
        return null;
    }

    // 清空cookie
    function clearCookie(){
        $.ajax({
            url: "/clear/cookie",
            type: "GET",
        });
    }

    function clearLocalStorage() {
        localStorage.clear();
        console.log("LocalStorage 已清空");
    }


</script>
</body>
</html>
