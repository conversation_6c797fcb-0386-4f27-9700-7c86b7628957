<#assign  _project_index_=0>
<style>
    .el-popover[x-placement^=bottom] {
        margin-top: -3px;
    }
    .header-menu{overflow: hidden}

    .header-icon-project{
        background-image: url(${_STATIC_COMMON_}/img/common-img/topImg/location.svg);
        background-repeat: no-repeat;
        width: 20px;
        height: 20px;
        display: inline-block;
        vertical-align: middle;
        margin-right: 4px;
    }
    .header-icon-back {
        background-image: url(${_STATIC_COMMON_}/img/common-img/topImg/back.svg);
        background-repeat: no-repeat;
        width: 20px;
        height: 20px;
        display: inline-block;
        vertical-align: middle;
        margin-right: 12px;
    }
    .header-icon-notice{
        background-image: url(${_STATIC_COMMON_}/img/common-img/topImg/notice.svg);
        background-repeat: no-repeat;
        width: 17px;
        height: 20px;
        display: inline-block;
        vertical-align: middle;
        margin-right: 12px;
    }

    .header-icon-bar{
        background-image: url(${_STATIC_COMMON_}/img/common-img/topImg/bar.svg);
        background-repeat: no-repeat;
        width: 20px;
        height: 20px;
        display: inline-block;
        margin-left: 20px;
        margin-top: 23px;
    }

    .header-icon-down{
        background-image: url(${_STATIC_COMMON_}/img/common-img/siderImg/down.svg);
        background-repeat: no-repeat;
        width: 10px;
        height: 6px;
        display: inline-block;
        margin-left: 5px;
    }
    .logo div{
        background-repeat: no-repeat;
    }

</style>
<header id="VueElement_Header" class="header">
    <el-popover style="display:none" width="476" ref="project-popover" placement="bottom-end" v-model="projectVisible" trigger="click" :visible-arrow="false" popper-class="header-popover">
        <shang-scroller :max-height="510">
            <div style="padding:12px;">
                <div class="header-project-main">
                    <#if haveHome == true>
                        <i class="fa fa-map-marker"></i>
                        <a class="header-project-main-link <#if !pcurrent??>header-project-main-selected</#if>" href="#" @click="enterProject($event)">${i18n('page.index.head.projectHome')}</a>
                    </#if>
                    <div style="float: right;position: relative;" class="el-input">
                        <input type="text" autocomplete="off"
                               placeholder="${i18n('page.index.head.search')}"
                               style="border-radius: 4px;width: 250px;"
                               class="el-input__inner" v-model="searchProjectText" @keydown.13="searchProject($event)" >
                    </div>
                </div>
                <#if progros?exists>
                    <#list progros?keys as key>
                        <div>
                            <#if key =="">
                                <div class="header-project-group" data-group-name="-1">${i18n('page.index.head.noGourp')}</div>
                            <#else>
                                <div class="header-project-group" data-group-name="${key}">${key?html}</div>
                            </#if>
                            <div class="header-project-items clearfix">
                                <#list progros[key] as p>
                                    <div class="header-project-item"
                                         data-project-name="${p.id}"
                                         title="${p.name?html}"
                                         v-bind:class="{'new-line': ${p_index} % 3 == 0,'end-line': (${p_index}+1)%3==0,'header-project-item-selected': ${p.id} === <#if pcurrent??>${pcurrent.id}<#else>''</#if>}"
                                         @click="enterProject($event,${p.id})">
                                        <a class="header-project-link" href="#" @click="enterProject($event,${p.id})" title="${p.name?html}">${p.name?html}</a>
                                    </div>
                                </#list>
                            </div>
                        </div>
                    </#list>
                </#if>
            </div>
        </shang-scroller>
    </el-popover>
    <el-popover style="display:none" ref="notification-popover" placement="bottom-end" width="345" v-model="notificationVisible" trigger="click" :visible-arrow="false" popper-class="header-notification-popover">
        <div class="header-notification-item clearfix" v-for="item in notifications" @click="readMessage">
            <div class="header-notification-item-time">
                {{item.timeAgo}}
            </div>
            <div class="header-notification-item-content">
                <div class="header-notification-item-image"><img alt="" width="34" height="34" src="${_STATIC_COMMON_}/img/default-head.png"></div>
                <div class="header-notification-item-message">
                    {{item.content}}
                </div>
            </div>
        </div>
        <div class="header-notification-more" @click="readMessage">${i18n('page.index.head.allmsg')} <i class="fa fa-angle-right" aria-hidden="true"></i></div>
    </el-popover>
    <el-popover style="display:none" ref="avatar-popover" placement="bottom-end" width="375" v-model="avatarVisible" trigger="click" :visible-arrow="false" popper-class="header-avatar-popover">
        <div class="header-user clearfix">
            <img class="header-user-image" src="${_STATIC_COMMON_}/img/default-head.png"></img>
            <div class="header-user-info">
                <div class="header-user-name">${userName}</div>
                <div class="header-logout"><a  @click="exit()">${i18n('page.index.head.exit')}</a></div>
            </div>
        </div>
        <div class="header-user-links">
            <ul class="header-user-links-group">
                <li class="header-user-link">
                    <a @click="showPerson($event)" href="#">${i18n('page.index.head.profile')}</a>
                </li>
            </ul>
            <ul class="header-user-links-group">
                <li class="header-user-link">
                    <a target="_blank" href="https://help.fmone.cn/">${i18n('page.index.head.helpcenter')}</a>
                </li>
                <#--<li class="header-user-link">
                    <a target="_blank" href="http://www.fmone.cn:3000/updates/web.html">${i18n('page.index.head.updatelog')}</a>
                </li>-->
                <#--<li class="header-user-link">-->
                    <#--<a>新手指导</a>-->
                <#--</li>-->
            </ul>
            <ul class="header-user-links-group">
                <li class="header-user-link">
                    <a target="_blank" href="http://www.facilityone.cn/customCase.html">${i18n('page.index.head.customservice')}</a>
                </li>
                <li class="header-user-link">
                    <a target="_blank" href="http://www.facilityone.cn/contactUs.html">${i18n('page.index.head.feedback')}</a>
                </li>
            </ul>
        </div>
    </el-popover>
    <div class="header-logo"  :class="{ 'header-logo-narrow': isNarrow }" :style="styleObject" id="header-logo"></div>
    <div class="header-menu clearfix" :class="{ 'header-menu-narrow': isNarrow }">
        <div class="header-narrow" @click="menuNarrow">
            <i class="header-icon-bar" aria-hidden="true"></i>

        </div>
        <ul class="header-nav">
            <li class="header-nav-item" v-bind:class='{open: projectVisible}' v-popover:project-popover>
                <div class="header-project">
                    <i class="header-icon-project" aria-hidden="true"></i>
                <#if pcurrent??>
                ${pcurrent.name?html}
                <#else>
                ${i18n('page.index.head.projectHome')}
                </#if>
                </div>
            </li>
            <#if coeEnabled>
                <li class="header-nav-item" @click="returnPortal()">
                    <div class="header-notification" style="margin-bottom:-1px">
                        <div>
                            <i class="header-icon-back" aria-hidden="true"></i>${i18n('page.index.head.back')}
                        </div>
                    </div>
                </li>
            </#if>
            <li class="header-nav-item" v-bind:class='{open: notificationVisible}' v-popover:notification-popover>
                <div class="header-notification" style="margin-bottom:-1px">
                    <div v-if="!notificationCount">
                        <i class="header-icon-notice" aria-hidden="true"></i>${i18n('page.index.head.notification')}
                    </div>
                    <el-badge :value="notificationCount" class="item" style="display:none" v-bind:style="{'display':notificationCount?'inline':'none'}" :max="99">
                        <i class="fa fa-bell" aria-hidden="true"></i>${i18n('page.index.head.notification')}
                    </el-badge>
                </div>
            </li>
            <li class="header-nav-item" v-bind:class='{open: avatarVisible}' v-popover:avatar-popover>
                <div class="header-avatar">
                    <div class="header-avatar-image">
                        <img src="${_STATIC_COMMON_}/img/default-head.png" alt="" width="30" height="30">
                    </div>
                    <div class="header-avatar-info">
                    ${userName} <i class="header-icon-down" aria-hidden="true"></i>
                        <input type="hidden" id="user_name" value="${userName}"/>
                        <input type="hidden" id="user_id" value="${userId?c}"/>
                    </div>
                </div>
            </li>
        </ul>
    </div>

</header>

<script type="text/javascript" src="${_STATIC_COMMON_}/js/js-plugin/jquery/jquery-1.8.3.js"></script>
<script>
    $(document).ready(function(){

        var getAccessToken = function () {
            var accesstoken = localStorage.access_token;
            if (!accesstoken) {
                accesstoken = FO.get_uri_param("access_token");
            }
            if(isWhiteUser == "true"){
                accesstoken = FO.get_uri_param("access_token");
            }
            return accesstoken;
        }
        //headpic
        if ("${headPic!}" != "") {
            var path = "${headPic!}";
            $(".header-avatar-image").find("img").attr("src", path);
            $(".header-user-image").attr("src", path);
        }
    });

</script>
<script type="text/javascript">
    window._AMapSecurityConfig = {
        securityJsCode:'57bc969d7f3c18ea1b92699a0d4ac8b9',
    }
</script>
<script src="https://webapi.amap.com/maps?v=1.4.15&key=f52eb3f9d9d4890397051cea0d66b26f"></script>
<script type="text/javascript" src="//cache.amap.com/lbs/static/PlaceSearchRender.js"></script>
<script src="//a.amap.com/jsapi_demos/static/demo-center/js/demoutils.js"></script>


<script type="text/javascript">
    window._AMapSecurityConfig = {
        securityJsCode:'9653c4ce203b05bc9ee856767278e3c2',
    }
</script>
<script src="https://webapi.amap.com/maps?v=1.4.15&key=f0b698e9ddc603bc5c4ff355e38bd066"></script>
<script type="text/javascript" src="//cache.amap.com/lbs/static/PlaceSearchRender.js"></script>
<script src="//a.amap.com/jsapi_demos/static/demo-center/js/demoutils.js"></script>

