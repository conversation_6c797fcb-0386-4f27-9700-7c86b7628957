<style>
    .logo div{
        background-repeat: no-repeat;
    }
    .programsGroup .dropdown-menu{
        width: 476px;
        max-width: 800px;
        max-height: 500px;
        overflow-y: auto;
        overflow-x: hidden;
        border: none;
        background-color: #FFF;
        border: 1px solid #d3dce6;
        box-shadow: 0 2px 4px 0 rgba(0,0,0,.12), 0 0 6px 0 rgba(0,0,0,.04);
    }
    .programsGroup .dropdown-menu ul{
        width: 350px;
        max-width: 800px;
        max-height: 500px;

    }
    .programsGroup .dropdown-menu li a:hover{
        background-color:#fff;
        color: #1ab394;
        border: 1px solid #1ab394;
    }
    .programsGroup .dropdown-menu li a{
        border:1px solid #eee;
        background-color:#eee;
        border-radius: 5px;
    }
    .programsGroup .dropdown-menu li.selected a{
        background-color: #5E87B0;
        color:#FFF;
    }

    .programsGroup .dropdown-menu dd li a{
        border:1px solid #e5e5e5;
        background-color:#fff;
        margin-top: 20px;
        margin-bottom: 10px;
        margin-right: 27px;
        border-radius: 2px;
    }
    .programsGroup .dropdown-menu dd li.selected a{
        background-color: #1ab394;
        color:#FFF;
        border: 1px solid #1ab394;
    }

    .dropdown.user .fa-info{
        margin-left: 4px;
        margin-right: 12px;
    }
    .fa-newspaper-o::before{
        content: "\f133";
    }

    .programsGroup .dropdown-menu dt {
        border-bottom: 1px solid #e5e5e5;
        color: #333;
        font-size: 14px;
        font-weight: 700;
        padding: 8px 0;
    }

    .header-icon-project {
        background-image: url(${baseStaticPath}/resource/img/common-img/topImg/location.svg);
        background-repeat: no-repeat;
        width: 20px;
        height: 20px;
        display: inline-block;
        vertical-align: middle;
        margin-right: 4px;
    }
    .header-icon-back {
        background-image: url(${baseStaticPath}/resource/img/common-img/topImg/back.svg);
        background-repeat: no-repeat;
        width: 20px;
        height: 20px;
        display: inline-block;
        vertical-align: middle;
        margin-right: 8px;
    }
    .header-icon-notice {
        background-image: url(${baseStaticPath}/resource/img/common-img/topImg/notice.svg);
        background-repeat: no-repeat;
        width: 17px;
        height: 20px;
        display: inline-block;
        vertical-align: middle;
        margin-right: 8px;
    }
    .header-icon-down {
        background-image: url(${baseStaticPath}/resource/img/common-img/siderImg/down.svg);
        background-repeat: no-repeat;
        width: 10px;
        height: 6px;
        display: inline-block;
        margin-left: 5px;
    }
    .header-user {
        border-bottom: 1px solid #e5e5e5;
        padding: 20px;
        font-size: 14px;
    }
    .clearfix:before, .clearfix:after {
        content: " ";
        display: table;
    }
    .header-user-info {
        margin-left: 130px;
        position: relative;
        height: 96px;
    }
    .header-user-name {
        font-size: 16px;
        padding-top: 20px;
    }
    .header-logout {
        color: #ff5b5b;
        position: absolute;
        bottom: 0;
        right: 0;
    }
    .header-logout a {
        color: #ff5b5b !important;
    }
    .header-user-image {
        width: 96px !important;
        height: 96px !important;
        border-radius: 50% !important;
        border: 1px solid #e5e5e5;
        float: left;
    }
    .header-user-links {
        padding: 0 20px 20px 20px;
    }
    .header-user-links-group {
        border-bottom: 1px solid #e5e5e5;
        padding: 30px 0 8px !important;
    }

    .header-user-link {
        padding: 8px 0;
        list-style: none;
        position: relative;
        display: flex;
    }
    .header-user-link:before {
        content: " ";
        border: 1px solid #999;
        width: 11px;
        height: 11px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 14px;
        color: #999;
        position: absolute;
        top: 13px;
    }
    .header-user-link a {
        font-size: 14px;
        color: #666;
    }

    .header-user-link .header-a {
        padding-left: 24px;
    }
    .header-icon-bar {
        background-image: url(${baseStaticPath}/resource/img/common-img/topImg/bar.svg);
        background-repeat: no-repeat;
        width: 20px;
        height: 20px;
        display: inline-block;
        margin-left: 2px;
        margin-top: -1px;
    }
    .pull-left #contentTip:hover {
         white-space:normal;
         height:148px;
         background-color:#F2F9F9;
         transition-property:background-color,height;
         transition-duration:0.2s ;
         transition-timing-function:linear;
    }

    .logo_facilityone {
        background-repeat: no-repeat !important;
        background-size: contain !important;
    }
</style>
<header id="header" class="header skin-black pace-done">
    <div class="logo">
        <div class="logo_facilityone" id="logo_facilityone"></div>
    </div>
    <nav class="navbar navbar-static-top" role="navigation">
        <!-- Sidebar toggle button-->
        <a href="#" class="navbar-btn sidebar-toggle sidebar-collapse" data-toggle="offcanvas" role="button" id="sidebar-collapse">
            <#--<span class="sr-only">Toggle navigation</span>-->
            <#--<span class="icon-bar"></span>-->
            <#--<span class="icon-bar"></span>-->
            <#--<span class="icon-bar"></span>-->
            <i aria-hidden="true" class="header-icon-bar"></i>
        </a>

        <div class="navbar-right">
            <ul class="nav navbar-nav pull-right">
                <li id="header-projects" class="dropdown programsGroup" style="margin-right: 20px;">
                    <a data-toggle="dropdown" class="dropdown-toggle" href="#">
                        <i class="header-icon-project" aria-hidden="true"></i>
                        <span class="username">
                        <#if pcurrent??>
                            ${pcurrent.name?html}
                        <#else>
                            ${i18n('page.index.head.projectHome')}
                        </#if>
                         </span>
                    </a>
                    <ul class="dropdown-menu">
                        <dl style="margin-left: 10px;padding-top: 5px;position: relative;"><dd>
                                <li class="<#if !pcurrent??>selected</#if> pull-left">
                                    <#if haveHome == true>
                                        <a href="javascript:void(0);"> ${i18n('page.index.head.projectHome')} </a>
                                    </#if>
                                    <#-- 当登录人只有一个项目时，样式调整 -->
                                    <#if haveHome == false>
                                        <div style="opacity: 0;margin-right: 27px;height: 30px;line-height: 28px;width: 120px;float: left;padding: 0 6px;margin-top: 20px;margin-bottom: 10px;">&nbsp;&nbsp;</div>
                                    </#if>
                                    <input type="text"
                                           placeholder="${i18n('page.index.head.search')}"
                                           style="display: block;box-sizing: border-box;width: 250px;height: 34px;color: #1f2d3d;
                                        border: 1px solid #e6e6e6 !important;outline: 0;transform: translate(35px, 20px);
                                        border-radius: 4px;padding: 3px 10px;"
                                           id="header-project-search">
                                </li>
                            </dd>
                        </dl>
                        <#if progros?exists>
                            <#list progros?keys as key>
                                <dl style="margin-left: 10px; padding-top: 5px;clear: both">
                                    <#if key =="">
                                        <dt data-group-name="-1">${i18n('page.index.head.noGourp')}</dt>
                                    <#else>
                                        <dt data-group-name="${key}"> ${key?html}</dt>
                                    </#if>
                                    <dd>
                                        <#list progros[key] as p>
                                            <li class="<#if pcurrent?? && p.id==pcurrent.id>selected</#if> pull-left"
                                                data-project-name="${p.id}"><a href="javascript:void(0);" data-pid="${p.id?c}" title="${p.name?html}">${p.name?html}</a></li>
                                        </#list>
                                    </dd>
                                </dl>
                            </#list>
                        </#if>
                    </ul>
                </li>
                <#if coeEnabled>
                    <li id="header-return-portal" class="dropdown">
                        <a data-toggle="dropdown" class="dropdown-toggle" href="#">
                            <#--<i class="fa fa-envelope"></i>-->
                            <i aria-hidden="true" class="header-icon-back"></i>
                            <span>${i18n('page.index.head.back')}</span>
                        </a>
                    </li>
                </#if>
                <li id="header-message" class="dropdown">
                    <a data-toggle="dropdown" class="dropdown-toggle" href="#">
                        <#--<i class="fa fa-envelope"></i>-->
                        <i aria-hidden="true" class="header-icon-notice"></i>
                        <span>${i18n('page.index.head.notification')}</span>
                        <span class="badge unreadnumber" style="display:none;">0</span>
                    </a>
                    <ul class="dropdown-menu inbox">
                        <#--<li class="dropdown-title">-->
                            <#--<span><i class="fa fa-envelope-o"></i> <span class="unreadnumber">0</span> ${i18n('page.index.head.unreadmsg')}</span>-->
                        <#--</li>-->
                        <div id="unreadmessage" class="messageTip" style="white-space: nowrap;"></div>
                        <li onclick="showMeaageSite()" class="footer">
                            <#--<a>${i18n('page.index.head.allmsg')}<i class="fa fa-arrow-circle-right"></i></a>-->
                            <a style="float: none">${i18n('page.index.head.allmsg')}<i aria-hidden="true" class="fa fa-angle-right"></i></a>
                        </li>
                    </ul>
                </li>

                <li id="header-user" class="dropdown user">
                    <a data-toggle="dropdown" class="dropdown-toggle" href="#">
                        <img src="${_STATIC_COMMON_}/img/default-head.png" alt="">
                        <span class="username">${userName}</span>
                        <#--<i class="s-angle-down"></i>-->
                        <i aria-hidden="true" class="header-icon-down"></i>
                        <input type="hidden" id="user_name" value="${userName}"/>
                        <input type="hidden" id="user_id" value="${userId?c}"/>
                    </a>
                    <ul class="dropdown-menu" style="width: 375px;max-width: 375px;box-shadow: 0 2px 4px 0 rgba(0,0,0,.12), 0 0 6px 0 rgba(0,0,0,.04);border: 1px solid #d3dce6;">
                        <div style="padding: 10px;">
                            <div class="header-user clearfix">
                                <img class="header-user-image" src="${_STATIC_COMMON_}/img/default-head.png"></img>
                                <div class="header-user-info">
                                    <div class="header-user-name">${userName}</div>
                                    <div class="header-logout"><a onclick="exit()">${i18n('page.index.head.exit')}</a></div>
                                </div>
                            </div>
                            <div class="header-user-links">
                                <ul class="header-user-links-group" style="padding: 30px 0 8px !important;">
                                    <li class="header-user-link">
                                        <a class="header-a" onclick="showPerson()">${i18n('page.index.head.profile')}</a>
                                        <#--<a @click="showPerson($event)" href="#">${i18n('page.index.head.profile')}</a>-->
                                    </li>
                                </ul>
                                <ul class="header-user-links-group" style="padding: 30px 0 8px !important;">
                                    <li class="header-user-link">
                                        <a class="header-a" target="_blank" href="https://help.fmone.cn/">${i18n('page.index.head.helpcenter')}</a>
                                    </li>
                                </ul>
                                <ul class="header-user-links-group" style="padding: 30px 0 8px !important;">
                                    <li class="header-user-link">
                                        <a class="header-a" target="_blank" href="http://www.facilityone.cn/customCase.html">${i18n('page.index.head.customservice')}</a>
                                    </li>
                                    <li class="header-user-link">
                                        <a class="header-a" target="_blank" href="http://www.facilityone.cn/contactUs.html">${i18n('page.index.head.feedback')}</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <#--<li><a onclick="showPerson()"><i class="fa fa-user"></i> ${i18n('page.index.head.profile')}</a></li>-->
                        <#--<li><a onclick="shwoHelpCenter()"><i class="fa fa-info"></i> ${i18n('page.index.head.helpcenter')}</a></li>-->
                        <#--<li><a href="http://www.fmone.cn:3000/updates/web.html" target="_blank"><i class="fa fa-newspaper-o"></i> ${i18n('page.index.head.updatelog')}</a></li>-->

                    </ul>
                </li>
            </ul>
        </div>
    </nav>
</header>
<script type="text/javascript" src="${_STATIC_COMMON_}/js/js-plugin/jquery/jquery-1.8.3.js"></script>
<script>
    $(document).ready(function(){

        //headpic
        if ("${headPic!}" != "") {
            var path = PUBLIC_PATH+"/common/files/id/${headPic!}/img";
            $(".header-user-image").attr("src", path);
        }

        $.ajax({
            url:'/logo/CustomSettings',
            type: 'get',
            success: function (data) {
                var  data=data.data;
                if ($.cookie('mini_sidebar') === '1') {

                    $("#logo_facilityone").css("width", "50px");
                    if (data && data.systemAbbreviatedLogo) {
                        $("#logo_facilityone").css("background", "url("+data.systemAbbreviatedLogo + ") no-repeat 50% 50%");
                    }else {
                        $("#logo_facilityone").css("background", "url("+PUBLIC_PATH+"/resource/img/logo-collapse.png) no-repeat 50% 50%");

                    }
                }
                else {
                    $("#logo_facilityone").css("width", "220px");
                    if (data && data.systemNormalLogo) {
                        $("#logo_facilityone").css("background", "url("+ data.systemNormalLogo + ") no-repeat 30% 50%");
                    }else {
                        $("#logo_facilityone").css("background", "url("+PUBLIC_PATH+"/resource/img/logo.png) no-repeat 30% 50%");
                    }
                }
            }
        });

        $('#menu-project-silmsrcoll').slimScroll({height:'476px' });

        $('#contentTip').tooltip();

        //首页搜索
        $.ajax({
            'url':'/main/header/menuSearch',
            type:'POST',
            data:JSON.stringify({searchText:''}),
            success:function (res) {
                if(res.data) {
                    window.PROJECT_SEAECH = res.data;
                }
            }
        });

        $('#header-project-search').on('click', function (e) {
            e.stopPropagation();
        });

        $('#header-project-search').on('keydown', function (e) {
            if(e.keyCode && e.keyCode === 13) {
                var reg = new RegExp($('#header-project-search').val(), 'i');

                if(window.PROJECT_SEAECH) {
                    var projectList = window.PROJECT_SEAECH;
                    for(var i = 0; i<projectList.length; i++) {
                        var flag = false;
                        for(var j = 0; j<projectList[i].projects.length; j++) {
                            if(reg.test(projectList[i].projects[j].projectName)) {
                                flag = true;
                                $('[data-project-name='+projectList[i].projects[j].projectId +']').show();
                                $('[data-project-name='+projectList[i].projects[j].projectId +']').parents('dl').show();
                            } else {
                                $('[data-project-name='+projectList[i].projects[j].projectId +']').hide();
                                if(!flag) {
                                    $('[data-project-name='+projectList[i].projects[j].projectId +']').parents('dl').hide();
                                }
                            }
                        }
                        if(!flag) {
                            $('[data-project-name='+projectList[i].projects[0].projectId +']').parent().prev().hide();
                            //$('[data-group-name='+projectList[i].groupId +']').hide();
                        } else {
                            $('[data-project-name='+projectList[i].projects[0].projectId +']').parent().prev().show();
                            //$('[data-group-name='+projectList[i].groupId +']').show();
                        }
                    }
                }
            }
        })

        $('#header-return-portal').on('click', function (e) {
            //百度追踪
            _hmt && _hmt.push(['_trackPageview', coeUrl]);

            window.location.href=coeUrl;
        });
    });

</script>
