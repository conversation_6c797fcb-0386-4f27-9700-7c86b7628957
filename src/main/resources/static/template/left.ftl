<div class="sidebar sidebar-fixed hide-menu-icc" id="sidebar">
    <style>
        .hide-menu-icc {
            display: none !important;
        }
    </style>
    <div class="sidebar-menu nav-collapse">
        <div class="divide-10"></div>
        <!-- SEARCH BAR -->
        <div id="search-bar" style="display:none;">
            <input type="text" placeholder="Search" class="search"><i class="fa fa-search search-icon"></i>
        </div>
        <!-- /SEARCH BAR -->

        <!-- SIDEBAR QUICK-LAUNCH -->
        <!-- <div id="quicklaunch">
        <!-- /SIDEBAR QUICK-LAUNCH -->

        <!-- SIDEBAR MENU -->
        <ul class="mymenu">
            <#if menus??>
            <#list menus?keys as key>
                <#assign menu=menus[key]>
                <li class=" <#if menu.subMenus??>has-sub</#if>">
                    <a href='<#if (menu.link?length) gt 0>${menu.link}<#else>javascript:;</#if>' target='${menu.target!}' class="clearfix">
                        <i class="fa ${menu.icon!}"></i> <span class="menu-text">${menu.name}</span>
                        <#if menu.subMenus??>
                            <span class="arrow"></span>
                        </#if>
                    </a>
                    <#if menu.subMenus??>
                        <ul class="sub" style="display:none;">
                            <#list menu.subMenus?keys as key2>
                            <#assign smenu=menu.subMenus[key2]>
                            <li class="<#if smenu.subMenus??>has-sub-sub</#if>">
                                <a href='<#if (smenu.link?length) gt 0>${smenu.link}<#else>javascript:;</#if>' target='${smenu.target!}' class="sublevel">
                                    <#--<i class="fa ${smenu.icon!}"></i> -->
                                    <span class="sub-menu-text">${smenu.name}</span>
                                <#if smenu.subMenus??>
                                    <span class="arrow"></span>
                                </#if>
                                </a>
                                <#if smenu.subMenus??>
                                <ul class="sub-sub" style="display:none;">
                                    <#list smenu.subMenus?keys as key3>
                                    <#assign ssmenu=smenu.subMenus[key3]>
                                        <li><a href="${ssmenu.link}" target='${ssmenu.target!}'>
                                            <#--<i class="fa ${ssmenu.icon!}"></i> -->
                                            <span class="sub-sub-menu-text">${ssmenu.name}</span></a>
                                        </li>
                                    </#list>
                                </ul>
                                </#if>
                            </li>
                            </#list>
                        </ul>
                    </#if>
                </li>
            </#list>
            </#if>
        </ul>
        <!-- /SIDEBAR MENU -->
        <div class="s-favorite" style="display:none;">
            <span><i class="fa fa-location"></i></span>
            <span><i class="fa fa-employee"></i></span>
            <span><i class="fa fa-favorite-add"></i></span>
        </div>
        <div class="divide-10"></div>
    </div>
</div>
<script type="text/javascript" src="${_STATIC_COMMON_}/js/js-plugin/jquery/jquery-1.8.3.js"></script>
<script>
    $(".mymenu a").click(function (event) {
        event.preventDefault();
    });
    $(document).ready(function(){

        /*$(".sub li").click(function(){
            $("body").scrollTop(0);
        });*/

        var type = FO.get_uri_param("type");
        if(isWhiteUser == "false" || typeof (type)=='undefined'){
            $("#header").show();
            $("#sidebar").removeClass('hide-menu-icc');
            $("#main-content").css({
                "margin-left": "220px",
                "margin-top": "0px",
                "padding-top": "50px"
            })
        }
    });
</script>
