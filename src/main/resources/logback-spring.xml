<?xml version="1.0" encoding="UTF-8"?>
<!-- Logback Configuration. See http://logback.qos.ch/manual/index.html -->

<configuration scan="false" scanPeriod="60 seconds" debug="true">
    <springProperty scope="context" name="log.home" source="fm-log.home" defaultValue="${user.dir}/logs"/>
    <springProperty scope="context" name="log.module" source="fm-log.module" defaultValue="fmcore"/>
    <springProperty scope="context" name="log.max-size" source="fm-log.file-max-size" defaultValue="10MB"/>
    <springProperty scope="context" name="log.max-history" source="fm-log.file-max-history" defaultValue="168"/>
    <springProperty scope="context" name="log.enabled.console" source="fm-log.console-enabled" defaultValue="true"/>
    <springProperty scope="context" name="log.enabled.console-level" source="fm-log.console-level" defaultValue="DEBUG"/>
    <springProperty scope="context" name="log.enabled.debug" source="fm-log.file-debug-enabled" defaultValue="true"/>
    <springProperty scope="context" name="log.enabled.warn" source="fm-log.file-warn-enabled" defaultValue="true"/>
    <springProperty scope="context" name="log.enabled.error" source="fm-log.file-error-enabled" defaultValue="true"/>
    <springProperty scope="context" name="log.enabled.monitor" source="fm-log.file-monitor-enabled" defaultValue="true"/>
    <springProperty scope="context" name="log.monitor-max-history" source="fm-log.file-monitor-max-history" defaultValue="7"/>

    <!-- CONSOLE -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>${log.enabled.console-level}</level>
        </filter>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %highlight(%-5level) %magenta(${PID}) --- [%thread] %cyan(%logger{50}) : %msg%n</pattern>
        </encoder>
    </appender>

    <!-- file : Debug & All -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.home}/${log.module}/%d{yyyy-MM-dd-HH}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${log.max-size}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>${log.max-history}</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level ${PID} [%thread] %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- file : Warn -->
    <appender name="FILE-WARN" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.home}/${log.module}/warn/warn-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level ${PID} [%thread] %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- file : Error -->
    <appender name="FILE-ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.home}/${log.module}/error/error-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level ${PID} [%thread] %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- FILE : MonitorLog -->
    <appender name="FILE-MONITOR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.home}/${log.module}/monitor/monitor-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${log.max-size}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>${log.monitor-max-history}</maxHistory>
        </rollingPolicy>
        <encoder>
            <!--<pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} | %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Enable File Monitor -->
    <if condition='property("log.enabled.monitor").contains("true")'>
        <then>
            <logger name="cn.facilityone.fm.core.monitor.MonitorLog" level="DEBUG">
                <appender-ref ref="FILE-MONITOR"/>
            </logger>
        </then>
    </if>

    <!-- Enable FILE and CONSOLE Appender -->
    <root level="WARN">
        <if condition='property("log.enabled.console").contains("true")'>
            <then>
                <appender-ref ref="CONSOLE"/>
            </then>
        </if>
        <if condition='property("log.enabled.debug").contains("true")'>
            <then>
                <appender-ref ref="FILE"/>
            </then>
        </if>
        <if condition='property("log.enabled.warn").contains("true")'>
            <then>
                <appender-ref ref="FILE-WARN"/>
            </then>
        </if>
        <if condition='property("log.enabled.error").contains("true")'>
            <then>
                <appender-ref ref="FILE-ERROR"/>
            </then>
        </if>
    </root>

    <!-- Custom Log Level of Packages -->
    <!-- springframework -->
    <logger name="org.springframework.boot" level="INFO"/>

    <!-- baomidou : mybatis-plus & kisso -->
    <logger name="com.baomidou.mybatisplus" level="DEBUG"/>
    <logger name="com.baomidou.kisso" level="DEBUG"/>

    <!-- flyway -->
    <logger name="org.flyway.db" level="DEBUG"/>

    <!-- jetcache -->
    <logger name="com.alicp.jetcache" level="DEBUG"/>

    <!-- hikari datasource -->
    <logger name="com.zaxxer.hikari" level="WARN"/>

    <!-- fm all -->
    <logger name="cn.facilityone.fm" level="DEBUG"/>
</configuration>