/*
 * Copyright 2006-2020 FacilityONE Inc. All Rights Reserved
 *
 * 注意：
 * 本软件内容仅限于费哲软件内部传阅，禁止外泄以及用于其他商业目的
 * 费哲软件(FacilityONE) : www.facilityone.cn
 */

package cn.facilityone.fm.maintain.test.permisson;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

/**
 * description goes here.
 *
 * <AUTHOR>
 * @date 2020/4/30 10:27
 * @since 1.0
 */
public class PermissionTest {

    public static void main(String[] args) {
        String pathStartName = "bulletin";
        List<String> valuesList = new ArrayList<>();
        String filePath = "C:\\Users\\<USER>\\Desktop\\123.sql";
        // 读取文件
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String str;
            while ((str = reader.readLine()) != null) {
                // 获取values内容
                valuesList.add(StringUtils.substringBetween(StringUtils.substringAfter(str, "VALUES"), "(", ")"));
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        List<String> result = handleValues(pathStartName, valuesList);
        // 写入文件
        try (BufferedWriter bufWrite =
                new BufferedWriter(new OutputStreamWriter(new FileOutputStream(filePath), "UTF-8"))) {
            for (String value : result) {
                bufWrite.write(value + "\r\n");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static List<String> handleValues(String prefix, List<String> valuesList) {
        List<String> resultList = new ArrayList<>();
        StringBuilder sql = null;
        for (String value : valuesList) {
            // 0 ID 2 是desciption 3 是avtive 4 是hide 5是type 6是name 7 是path 8是 menu_id
            String[] valueArray = value.split(",");
            System.out.println(valueArray[0]);
            sql = new StringBuilder(
                    "INSERT INTO `sys_permission`(`id`, `permission_name`, `permission_sign`, `type`, `active`, `hide`, `description`,`menu_id`,`deleted`, `created_by`, `created_date`, `modified_by`, `modified_date`, `version`) VALUES (");
            sql.append(valueArray[0]).append(",");
            sql.append(valueArray[6]).append(",");
            // perimission_sign 处理
            String permissionSign =
                    "'" + prefix + ":" + StringUtils.substringAfter(valueArray[7].replaceAll("/", ":"), ":");
            sql.append(permissionSign).append(",");
            sql.append(valueArray[5]).append(",");
            sql.append(valueArray[3]).append(",");
            sql.append(valueArray[4]).append(",");
            sql.append(valueArray[2]).append(",");
            sql.append(valueArray[8]).append(",");
            sql.append("0,NULL,sysdate(),NULL,sysdate(),0);");
            resultList.add(sql.toString());
        }
        return resultList;
    }
}
